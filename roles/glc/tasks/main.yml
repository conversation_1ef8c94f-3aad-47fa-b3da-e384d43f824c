---
- name: Upload efk docker compose file
  template:
    src: "{{ src_docker_compose }}"
    dest: "{{ dest_docker_compose }}"
    force: yes
    mode: '0644'
    backup: yes
    
- name: Create multiple directories
  file:
    path: "/etc/thestack/conf/fluentd"
    state: directory 
    mode: '0755'
    
- name: Upload efk config file
  template:
    src: "{{ src_fluentd_config }}"
    dest: "{{ dest_fluentd_config }}"
    force: yes
    mode: '0644'
    backup: yes

- name: Deploy services
  shell: docker stack deploy -c {{ dest_docker_compose }} {{ stack_name }}
  run_once: true

- name: Pause for 30 seconds to wait for services being running
  pause:
    seconds: 30
  run_once: true
  
- name: Ensure services deployed
  shell: docker service ls
  register: service_output

- name: Output service list
  debug:
    msg: "{{ service_output.stdout_lines }}"

- name: Show stacks
  shell: docker stack list
