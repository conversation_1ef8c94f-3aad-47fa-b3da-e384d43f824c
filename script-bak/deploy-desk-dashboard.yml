---
- name: deploy desk-dashboard
  hosts: managers
  vars:
    src_docker_compose: docker-compose.yml
    dest_docker_compose: /etc/thestack/compose/docker-compose-desk-dashboard.yml
    stack_name: desk-dashboard
    is_remove: "{{ remove }}"
  tasks:
    - name: Print install_status
      debug:
        msg: "{{ is_remove }}"

    - name: install desk-dashboard
      include_role:
        name: desk-dashboard
      when:  is_remove == 'false'

    - name: uninstall desk-dashboard
      shell: docker stack rm desk-dashboard
      when: "is_remove == 'true'"
      run_once: true  
