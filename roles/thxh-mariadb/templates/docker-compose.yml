version: "3.7"

networks:
  traefik_prod:
    external: true

services:
  db:
    #image: tianwen1:5000/mariadb:nv
    image: tianwen1:5000/thxh-mariadb:v2
    hostname: thxh-mariadb
#    ports:
#      - "3306:3306"
    networks:
      - traefik_prod
    volumes:
      - /mnt/ceph/mysql:/var/lib/mysql
    env_file:
      - /etc/thestack/db.env
    deploy:
      replicas: 1
      restart_policy:
        condition: any
        delay: 10s
        max_attempts: 3
        window: 180s
      placement:
        constraints:
          - node.role==manager
      labels:
        - traefik.enable=true
        - traefik.docker.network=traefik_prod
        - traefik.tcp.routers.thxh-mariadb.rule=HostSNI(`*`)
        - traefik.tcp.routers.thxh-mariadb.entrypoints=thxh-mariadb
        - traefik.tcp.routers.thxh-mariadb.tls=false
        - traefik.tcp.routers.thxh-mariadb.service=thxh-mariadb
        - traefik.tcp.services.thxh-mariadb.loadbalancer.server.port=3306
    healthcheck:
      test: ["CMD", "curl", "-f", "http://thehealthcheck:9900/v1/rbd/volumes/thedb"]
      interval: 1m
      timeout: 10s
      retries: 8
