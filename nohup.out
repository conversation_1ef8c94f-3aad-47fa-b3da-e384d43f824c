SLF4J: Failed to load class "org.slf4j.impl.StaticLoggerBinder".
SLF4J: Defaulting to no-operation (NOP) logger implementation
SLF4J: See http://www.slf4j.org/codes.html#StaticLoggerBinder for further details.
SLF4J: Failed to load class "org.slf4j.impl.StaticLoggerBinder".
SLF4J: Defaulting to no-operation (NOP) logger implementation
SLF4J: See http://www.slf4j.org/codes.html#StaticLoggerBinder for further details.
SLF4J: Failed to load class "org.slf4j.impl.StaticLoggerBinder".
SLF4J: Defaulting to no-operation (NOP) logger implementation
SLF4J: See http://www.slf4j.org/codes.html#StaticLoggerBinder for further details.

(Eclipse:8134): Gtk-CRITICAL **: 14:36:36.835: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:8134): Gtk-CRITICAL **: 14:36:36.843: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:8134): Gtk-CRITICAL **: 14:36:36.887: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:8134): Gtk-CRITICAL **: 14:36:36.899: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:8134): Gtk-CRITICAL **: 14:36:36.929: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:8134): Gdk-WARNING **: 15:37:42.022: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:42.022: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:42.022: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:42.027: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:42.027: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:42.027: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:42.028: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:42.028: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:42.028: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:42.028: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:42.028: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:42.028: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:42.028: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:42.028: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:42.028: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:42.028: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:42.028: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:42.028: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:42.028: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:42.029: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:42.029: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:42.029: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:42.029: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:42.029: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:42.029: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:42.029: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:42.029: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:42.029: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:42.029: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:42.029: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:42.029: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:42.029: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:42.029: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:42.030: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:42.030: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:42.030: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:42.030: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:42.030: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:42.030: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:42.030: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:42.030: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:42.030: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:42.030: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:42.030: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:42.030: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:42.030: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:42.030: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:42.030: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:42.031: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:43.996: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:43.997: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:43.997: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:44.002: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:44.002: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:44.002: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:44.003: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:44.003: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:44.003: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:44.003: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:44.003: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:44.003: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:44.003: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:44.003: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:44.003: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:44.003: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:44.003: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:44.003: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:44.004: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:44.004: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:44.004: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:44.004: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:44.004: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:44.004: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:44.004: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:44.004: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:44.004: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:44.004: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:44.004: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:44.004: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:44.004: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:44.005: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:44.005: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:44.005: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:44.005: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:44.005: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:44.005: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:44.005: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:44.005: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:44.005: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:44.005: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:44.005: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:44.005: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:44.005: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:44.005: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:44.005: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:44.005: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:44.005: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gdk-WARNING **: 15:37:44.005: Event with type 8 not holding a GdkDevice. It is most likely synthesized outside Gdk/GTK+

(Eclipse:8134): Gtk-CRITICAL **: 17:31:36.352: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:8134): Gtk-CRITICAL **: 17:31:36.384: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:8134): Gtk-CRITICAL **: 17:31:36.496: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:8134): Gtk-CRITICAL **: 17:31:36.521: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:8134): Gtk-CRITICAL **: 17:31:36.566: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:8134): Gtk-CRITICAL **: 17:31:36.759: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:8134): Gtk-CRITICAL **: 17:31:36.892: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:8134): Gtk-CRITICAL **: 17:31:36.966: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:8134): Gtk-CRITICAL **: 17:31:37.354: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:8134): Gtk-CRITICAL **: 17:31:37.382: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:8134): Gtk-CRITICAL **: 17:31:37.418: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:8134): Gtk-CRITICAL **: 17:31:37.526: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:8134): Gtk-CRITICAL **: 17:31:37.978: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:8134): Gtk-CRITICAL **: 17:31:38.020: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:8134): Gtk-CRITICAL **: 17:31:38.049: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:8134): Gtk-CRITICAL **: 17:31:38.167: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:8134): Gtk-CRITICAL **: 17:31:38.167: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:8134): Gtk-CRITICAL **: 17:31:38.176: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:8134): Gtk-CRITICAL **: 17:31:38.205: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:8134): Gtk-CRITICAL **: 17:31:38.205: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:8134): Gtk-CRITICAL **: 17:31:38.216: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:8134): Gtk-CRITICAL **: 17:31:38.217: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:8134): Gtk-CRITICAL **: 17:31:38.218: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:8134): Gtk-CRITICAL **: 17:31:38.224: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:8134): Gtk-CRITICAL **: 17:31:38.299: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:8134): Gtk-CRITICAL **: 17:31:38.300: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:8134): Gtk-CRITICAL **: 17:32:01.042: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:8134): Gtk-CRITICAL **: 17:32:01.072: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:8134): Gtk-CRITICAL **: 19:13:50.742: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:8134): Gtk-CRITICAL **: 19:13:50.782: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:8134): Gtk-CRITICAL **: 19:13:50.782: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:8134): Gtk-CRITICAL **: 19:13:50.827: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:8134): Gtk-CRITICAL **: 19:13:50.827: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:8134): Gtk-CRITICAL **: 19:13:50.827: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:8134): Gtk-CRITICAL **: 09:54:24.236: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:8134): Gtk-CRITICAL **: 09:54:24.440: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:8134): Gtk-CRITICAL **: 17:08:02.077: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:8134): Gtk-CRITICAL **: 17:08:02.206: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:8134): Gtk-CRITICAL **: 17:08:12.610: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:8134): Gtk-CRITICAL **: 17:08:12.630: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:8134): Gtk-CRITICAL **: 17:08:12.735: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:8134): Gtk-CRITICAL **: 17:08:12.771: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:8134): Gtk-CRITICAL **: 17:08:13.299: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:8134): Gtk-CRITICAL **: 17:08:13.338: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:8134): Gtk-CRITICAL **: 17:08:13.397: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:8134): Gtk-CRITICAL **: 17:08:14.054: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:8134): Gtk-CRITICAL **: 17:08:14.101: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:8134): Gtk-CRITICAL **: 17:08:14.590: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:8134): Gtk-CRITICAL **: 17:08:14.691: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:8134): Gtk-CRITICAL **: 17:08:15.181: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:8134): Gtk-CRITICAL **: 17:08:15.221: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:8134): Gtk-CRITICAL **: 17:08:15.281: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:8134): Gtk-CRITICAL **: 17:08:15.531: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:8134): Gtk-CRITICAL **: 17:08:15.531: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:8134): Gtk-CRITICAL **: 17:08:15.542: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:8134): Gtk-CRITICAL **: 17:08:15.544: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:8134): Gtk-CRITICAL **: 17:08:15.544: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:8134): Gtk-CRITICAL **: 17:08:15.549: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:8134): Gtk-CRITICAL **: 17:08:15.599: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar
