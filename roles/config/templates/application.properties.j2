server.port=10002
knife4j.enable=true
knife4j.basic.enable=true
knife4j.basic.username=guoenli
knife4j.basic.password=guoenli


#ceph.ip=*************
#ceph.ip=**************
#ceph.ip=***************
#ceph.pwd=thecloud2015.1
#ceph.pwd=root

ceph.ip={{ vip }}
ceph.nodename=root
ceph.pwd={{ ceph_webloginpwd }}
ceph.webloginname=admin
ceph.webloginpwd={{ ceph_webloginpwd  }}
ceph.fullip=https://${ceph.ip}:8443
#ceph.addosd2localfilepath=E:\\codes\\CssThe\\thecss\\src\\test
ceph.addosd2localfilepath=/etc/thecss
ceph.addosd2localfilename=data.json
#ceph.fullip=http://${ceph.ip}:8443
ceph.tokenurl=${ceph.fullip}/api/auth
#ceph.inventoryurl=${ceph.fullip}/api/orchestrator/inventory
ceph.inventoryurl=${ceph.fullip}/ui-api/host/inventory
ceph.osdUrl=${ceph.fullip}/api/osd
ceph.hostUrl=${ceph.fullip}/api/host?facts=true
ceph.poolUrl=${ceph.fullip}/ui-api/pool/info
ceph.nameSpacelUrl=${ceph.fullip}/api/block/pool/
ceph.healthfullUrl=${ceph.fullip}/api/health/full
ceph.smartUrl=${ceph.fullip}/api/osd/
ceph.crush_rule=${ceph.fullip}/api/crush_rule/
#¾í³ØÁÐ±íget
ceph.apipoolUrl=${ceph.fullip}/api/pool
#¿ª¹ØÊÇ·ñ¿ª¹Ø
ceph.switchStateUrl=${ceph.fullip}/api/cluster_conf/filter?names=mon_allow_pool_delete
#È·ÈÏ¾í³Ø°²È«ÀàÐÍÊÇ·ñÊÇEC
ceph.isEcTypeUrl=${ceph.fullip}/api/pool/{pool_name}?stats=true
#delÉ¾³ý¾í
ceph.delpoolUrl=${ceph.fullip}/api/pool/{pool_name}
#É¾³ýec¹æÔò
ceph.delOnlyEcRuleUrl=${ceph.fullip}/api/crush_rule/{crush-rule-name}
test.test.guotian=7676test112434

##solr
#spring.data.solr.host=http://***************:8983/solr
#spring.data.solr.core=doc-core