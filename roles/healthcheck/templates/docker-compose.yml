version: '3.7'
    
services:
  thehealthcheck:
    image: tianwen1:5000/thehealthcheck:latest
    hostname: thehealthcheck
    networks:
      - traefik_prod
    configs:
      - source: thehealthcheck.py
        target: /thesc/settings.py
    deploy:
      replicas: 1
      placement:
        constraints:
          - node.role==manager
      labels:
        - traefik.enable=true
        - traefik.docker.network=traefik_prod
        - traefik.tcp.routers.thehealthcheck.rule=HostSNI(`*`)
        - traefik.tcp.routers.thehealthcheck.entrypoints=thehealthcheck
        - traefik.tcp.routers.thehealthcheck.tls=false
        - traefik.tcp.routers.thehealthcheck.service=thehealthcheck
        - traefik.tcp.services.thehealthcheck.loadbalancer.server.port=9900

networks:
  traefik_prod:
    external: true

configs:
  thehealthcheck.py:
    file: /etc/thestack/conf/theweb/settings.py