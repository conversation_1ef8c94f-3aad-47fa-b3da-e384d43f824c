version: "3.7"

services:
  portainer:
    image: tianwen1:5000/portainer:latest
    # ports:
    #   - 9992:9000
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    restart: always

# configs:
#   theendconf_prometheus.yml:
#     external: true
#   theendconf_alertmanager.yml:
#     external: true
#   theendconf_alertmanager_template_default.tmpl:
#     external: true

# volumes:
#   thealertmanager:
#     external: true

# networks:
#   traefik_prod:
#     external: true
