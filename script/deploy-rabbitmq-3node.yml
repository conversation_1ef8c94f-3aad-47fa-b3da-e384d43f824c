---
- name: deploy rabbitmq
  hosts: deploy
  vars:
    src_docker_compose: docker-compose.yml
    dest_docker_compose: /etc/thestack/compose/docker-compose-rabbitmq-3node.yml

    src_rabbitmq_conf: rabbitmq.conf.j2
    dest_rabbitmq_conf: /etc/thestack/conf/rabbitmq/rabbitmq.conf

    src_rabbitmq_enabled_plugins: enabled_plugins.j2
    dest_rabbitmq_enabled_plugins: /etc/thestack/conf/rabbitmq/enabled_plugins

    stack_name: rabbitmq
    is_remove: "{{ remove }}"
  tasks:

    - name: Print install_status
      debug:
        msg: "{{ is_remove }}"

    - name: install rabbitmq
      include_role:
        name: "../roles/rabbitmq-3node"
      when:  is_remove == 'false'

    - name: uninstall rabbitmq
      shell: docker stack rm rabbitmq
      when: "is_remove == 'true'"
      run_once: true  
