version: "3.7"

networks:
  traefik_prod:
    external: true

services:
  db:
    image: tianwen1:5000/mariadb:v10curl
    hostname: db
    networks:
      - traefik_prod
    volumes:
      - thedb:/var/lib/mysql
    configs:
      - source: theconf_mariadb_init.sql
        target: /docker-entrypoint-initdb.d/init.sql
       
    deploy:
      mode: replicated
      replicas: 1
      restart_policy:
        condition: any
        delay: 10s
        max_attempts: 3
        window: 180s
      placement:
        constraints:
          - node.role==manager
      labels:
        - traefik.enable=true
        - traefik.docker.network=traefik_prod
        - traefik.tcp.routers.mariadb.rule=HostSNI(`*`)
        - traefik.tcp.routers.mariadb.entrypoints=mariadb
        - traefik.tcp.routers.mariadb.tls=false
        - traefik.tcp.routers.mariadb.service=mariadb
        - traefik.tcp.services.mariadb.loadbalancer.server.port=3306

      update_config:
        parallelism: 1
        delay: 3m
    environment:
      MYSQL_ROOT_PASSWORD: "thecloud2015.1"
    command: [--default-authentication-plugin=mysql_native_password, --max_connections=1000, --init-file=/docker-entrypoint-initdb.d/init.sql]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://thehealthcheck:9900/v1/rbd/volumes/thedb"]
      interval: 1m
      timeout: 10s
      retries: 8


configs:
  theconf_mariadb_init.sql:
    external: true


volumes:
  thedb:
    external: true
