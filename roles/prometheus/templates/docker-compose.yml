version: "3.7"

services:
  alertmanager:
    image: tianwen1:5000/alertmanager
    hostname: alertmanager
    networks:
      - prod
    volumes:
      - thealertmanager:/alertmanager
      #- /mnt/ceph/alertmanager/templates:/etc/alertmanager/templates
      - /usr/share/zoneinfo/Asia/Shanghai:/etc/localtime
    configs:
      - source: alertmanager.yml
        target: /etc/alertmanager/config.yml
      - source: alertmanager_template_default.tmpl
        target: /etc/alertmanager/templates/default.tmpl
    deploy:
      mode: replicated
      replicas: 1
      restart_policy:
        condition: any
        delay: 10s
        max_attempts: 3
        window: 120s
      placement:
        constraints:
          - node.role==manager
    healthcheck:
      test: ["CMD-SHELL", "/bin/nc -z alertmanager:9093 || exit 1"]
      interval: 1m
      timeout: 10s
      retries: 5


  prometheus:
    image: tianwen1:5000/prometheus:latest
    hostname: prometheus
    user: root
    networks:
      - prod
    volumes:
      - theprometheus:/prometheus
      - /usr/share/zoneinfo/Asia/Shanghai:/etc/localtime
      #- /etc/thestack/conf/prometheus/rules:/etc/prometheus/rules
    configs:
      - source: prometheus.yml
        target: /etc/prometheus/prometheus.yml
      # - source: alertmanager_rules.yml
      #   target: /etc/prometheus/rules.yml
    command:
      - "--config.file=/etc/prometheus/prometheus.yml"
      - "--web.enable-lifecycle"
      - "--log.level=debug"

    depends_on:
      - node-exporter
    deploy:
      mode: replicated
      replicas: 1
      restart_policy:
        condition: any
        delay: 10s
        max_attempts: 3
        window: 120s
      placement:
        constraints:
          - node.role==manager
      labels:
        - traefik.enable=true
        - traefik.docker.network=traefik_prod
        - traefik.tcp.routers.prometheus.rule=HostSNI(`*`)
        - traefik.tcp.routers.prometheus.entrypoints=prometheus
        - traefik.tcp.routers.prometheus.tls=false
        - traefik.tcp.routers.prometheus.service=prometheus
        - traefik.tcp.services.prometheus.loadbalancer.server.port=9090
    healthcheck:
      test: ["CMD-SHELL", "/bin/nc -z prometheus:9090 || exit 1"]
      interval: 1m
      timeout: 10s
      retries: 5

  # portainer:
  #   image: tianwen1:5000/portainer:latest
  #   ports:
  #     - 9999:9000
  #   volumes:
  #     - /var/run/docker.sock:/var/run/docker.sock
  #   restart: always

configs:
  prometheus.yml:
    file: /etc/thestack/conf/prometheus/prometheus.yml
  alertmanager.yml:
    file: /etc/thestack/conf/alertmanager/alertmanager.yml
  alertmanager_template_default.tmpl:
    file: /etc/thestack/conf/alertmanager/default.tmpl
  # alertmanager_rules.yml:
  #   file: /etc/thestack/conf/prometheus/rules.yml

volumes:
  thealertmanager:
    external: true
  theprometheus:
    external: true

networks:
  prod:
    name: prod
    external: true
