---

- name: mkdir -p /etc/thestack/compose
  shell: mkdir -p /etc/thestack/compose

- name: Upload config file
  copy:
    src: "{{ src_conf }}"
    dest: "{{ dest_conf }}"


- name: Check if destination file exists
  stat:
    path: "{{ dest_docker_compose }}"
  register: dest_docker_compose_file

- name: Upload docker compose file
  template:
    src: "{{ src_docker_compose }}"
    dest: "{{ dest_docker_compose }}"
  when: dest_docker_compose_file.stat.exists == False


# - name: Create OpenStack application credential
#   command :  docker exec -e OS_USERNAME=admin -e OS_PASSWORD={{ openstack_password }} -e OS_PROJECT_NAME=admin -e OS_USER_DOMAIN_NAME=Default -e OS_PROJECT_DOMAIN_NAME=Default -e OS_AUTH_URL=http://{{ vip }}:5000/v3 -e OS_IDENTITY_API_VERSION=3 -e LIBGUESTFS_BACKEND=direct nova_compute openstack application credential create  openstack_credential --role admin --format json
#   register: app_credential_output
#   when: "inventory_hostname == 'controller1'"

# - name: Set facts for application credential
#   set_fact:
#     application_credential_id: "{{ (app_credential_output.stdout | from_json).id }}"
#     user_id: "{{ (app_credential_output.stdout | from_json).user_id }}"
#     application_credential_secret: "{{ (app_credential_output.stdout | from_json).secret }}"
#   when: "inventory_hostname == 'controller1'"


# - name: Check if destination file exists
#   stat:
#     path: "{{ dest_prometheus_conf }}"
#   register: dest_prometheus_conf_file

# - name: upload prometheus conf
#   template:
#     src: "{{ src_prometheus_conf }}"
#     dest: "{{ dest_prometheus_conf }}"
#   when: dest_prometheus_conf_file.stat.exists == False

- name: Check if destination file exists
  stat:
    path: "{{ dest_traefik_routter_conf }}"
  register: dest_traefik_routter_conf_file


- name: upload traefik router conf
  template:
    src: "{{ src_traefik_routter_conf }}"
    dest: "{{ dest_traefik_routter_conf }}"
  when: dest_traefik_routter_conf_file.stat.exists == False

- name: Check if destination file exists
  stat:
    path: "{{ dest_haproxy_conf }}"
  register: dest_haproxy_conf_file


- name: upload haproxy conf
  template:
    src: "{{ src_haproxy_conf }}"
    dest: "{{ dest_haproxy_conf }}"
  when: dest_haproxy_conf_file.stat.exists == False


- name: Check if destination file exists
  stat:
    path: "{{ dest_thecss_conf }}"
  register: dest_thecss_conf_file

- name: upload thecss conf
  template:
    src: "{{ src_thecss_conf }}"
    dest: "{{ dest_thecss_conf }}"
  when: dest_thecss_conf_file.stat.exists == False

- name: Check if destination file exists
  stat:
    path: "{{ dest_thecephapi_conf }}"
  register: dest_thecephapi_conf_file

- name: upload thecephapi conf
  template:
    src: "{{ src_thecephapi_conf }}"
    dest: "{{ dest_thecephapi_conf }}"
  when: dest_thecephapi_conf_file.stat.exists == False

- name: Check if destination file exists
  stat:
    path: "{{ dest_theweb_conf }}"
  register: dest_theweb_conf_file

- name: upload theweb conf
  template:
    src: "{{ src_theweb_conf }}"
    dest: "{{ dest_theweb_conf }}"
  when: dest_theweb_conf_file.stat.exists == False

- name: Check if destination file exists
  stat:
    path: "{{ dest_keeplived_conf }}"
  register: dest_keeplived_conf_file


- name: upload keeplived conf
  template:
    src: "{{ src_keeplived_conf }}"
    dest: "{{ dest_keeplived_conf }}"
  when: dest_keeplived_conf_file.stat.exists == False

- name: Check if destination file exists
  stat:
    path: "{{ dest_storageapi_conf }}"
  register: dest_storageapi_conf_file

- name: upload storageapi conf
  template:
    src: "{{ src_storageapi_conf }}"
    dest: "{{ dest_storageapi_conf }}"
  when: dest_storageapi_conf_file.stat.exists == False

- name: Check if destination file exists
  stat:
    path: "{{ dest_thelicense_conf }}"
  register: dest_thelicense_conf_file

- name: upload thelicense conf
  template:
    src: "{{ src_thelicense_conf }}"
    dest: "{{ dest_thelicense_conf }}"
  when: dest_thelicense_conf_file.stat.exists == False

- name: Check if thedeskapi file exists
  stat:
    path: "{{ dest_thedeskapi_conf }}"
  register: dest_thedeskapi_conf_file


- name: upload thedeskapi conf
  template:
    src: "{{ src_thedeskapi_conf }}"
    dest: "{{ dest_thedeskapi_conf }}"
  when: dest_thedeskapi_conf_file.stat.exists == False



- name: Check if thedesk file exists
  stat:
    path: "{{ dest_thedesk_conf }}"
  register: dest_thedesk_conf_file


- name: upload thedesk conf
  template:
    src: "{{ src_thedesk_conf }}"
    dest: "{{ dest_thedesk_conf }}"
  when: dest_thedesk_conf_file.stat.exists == False

- name: Check if theguard file exists
  stat:
    path: "{{ dest_theguard_conf }}"
  register: dest_theguard_conf_file


- name: upload theguard conf
  template:
    src: "{{ src_theguard_conf }}"
    dest: "{{ dest_theguard_conf }}"
  when: dest_theguard_conf_file.stat.exists == False

- name: Check if usbserver file exists
  stat:
    path: "{{ dest_usbserver_conf }}"
  register: dest_usbserver_conf_file


- name: upload usbserver conf
  template:
    src: "{{ src_usbserver_conf }}"
    dest: "{{ dest_usbserver_conf }}"
  when: dest_usbserver_conf_file.stat.exists == False

- name: Check if thecloudapi file exists
  stat:
    path: "{{ dest_thecloudapi_conf }}"
  register: dest_thecloudapi_conf_file


- name: upload thecloudapi conf
  template:
    src: "{{ src_thecloudapi_conf }}"
    dest: "{{ dest_thecloudapi_conf }}"
  when: dest_thecloudapi_conf_file.stat.exists == False

- name: Check if theauth file exists
  stat:
    path: "{{ dest_theauth_conf }}"
  register: dest_theauth_conf_file

- name: upload theauth conf
  template:
    src: "{{ src_theauth_conf }}"
    dest: "{{ dest_theauth_conf }}"
  when: dest_theauth_conf_file.stat.exists == False

- name: Check if openstack_sh file exists
  stat:
    path: "{{ dest_openstack_sh_conf }}"
  register: dest_openstack_sh_conf_file


# - name: upload openstack_sh conf
#   template:
#     src: "{{ src_openstack_sh_conf }}"
#     dest: "{{ dest_openstack_sh_conf }}"
#   when: dest_openstack_sh_conf_file.stat.exists == False











- name: Deploy services
  shell: docker stack deploy -c {{ dest_docker_compose }} {{ stack_name }}
  run_once: true

- name: Pause for 10 seconds to wait for services being running
  pause:
    seconds: 10
  run_once: true

- name: Ensure services deployed
  shell: docker service ls
  register: service_output

- name: Output service list
  debug:
    msg: "{{ service_output.stdout_lines }}"

- name: Show stacks
  shell: docker stack list