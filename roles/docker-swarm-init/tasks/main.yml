---
- name: start docker service
  service:
    name: docker
    state: started
    enabled: yes

- name: Check if Swarm has already been Initialized
  shell: docker node ls
  register: swarm_status
  ignore_errors: true
  tags: swarm

- name: Initialize Docker Swarm with shell cmd
  shell: "docker swarm init --advertise-addr {{ hostvars['controller1']['ansible_host'] }}"
  when: swarm_status.rc != 0
  ignore_errors: True
  run_once: true
  tags: swarm


- name: debug swarm init addrs
  debug:
    msg: "{{ hostvars['controller1']['ansible_host'] }}"
    
    
- name: debug  default addrs
  debug:
    msg: "{{ hostvars[inventory_hostname]['ansible_default_ipv4']['address'] }}"

#- name: Initialize Docker Swarm
#  docker_swarm:
#    state: present
#    default_addr_pool: "{{ hostvars[inventory_hostname]['ansible_default_ipv4']['address'] }}/24" 
#  when: swarm_status.rc != 0
#  run_once: true
#  tags: swarm

#- name: Initialize Docker Swarm
#  community.general.docker_swarm:
#    state: present
#    advertise_addr: "{{ ansible_host }}"
#  register: result
#  when: inventory_hostname == groups['swarm-managers'][0]

- name: Get the Manager join-token
  shell: docker swarm join-token --quiet manager
  #shell: docker swarm join-token manager
  register: manager_token
  tags: swarm
  changed_when: false
  delegate_to: "{{ groups['managers'][0] }}"
  delegate_facts: true
  when: "'managers' in group_names
    and inventory_hostname != groups['managers'][0]"

  
- name: debug manager 0
  ignore_errors: True
  debug:
    msg: "{{ manager_token.stdout }}"

- name: Add the Manager 
  shell:  "docker swarm join --advertise-addr {{ hostvars[inventory_hostname]['ansible_host'] }}:2377 --token {{ manager_token.stdout  }} {{ hostvars['controller1']['ansible_host'] }}:2377"
  when: "'managers' in group_names
    and inventory_hostname != groups['managers'][0]"
  

