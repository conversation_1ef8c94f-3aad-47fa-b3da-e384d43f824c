---
- name: deploy rabbitmq
  hosts: managers
  vars:
    src_docker_compose: docker-compose.yml
    dest_docker_compose: /etc/thestack/compose/docker-compose-rabbitmq.yml
    stack_name: rabbitmq
    is_remove: "{{ remove }}"
  tasks:

    - name: Print install_status
      debug:
        msg: "{{ is_remove }}"

    - name: install rabbitmq
      include_role:
        name: rabbitmq
      when:  is_remove == 'false'

    - name: uninstall rabbitmq
      shell: docker stack rm rabbitmq
      when: "is_remove == 'true'"
      run_once: true  
