server.port=10002
knife4j.enable=true
knife4j.basic.enable=true
knife4j.basic.username=guoenli
knife4j.basic.password=guoenli


#ceph.ip=*************
#ceph.ip=**************
#ceph.ip=***************
#ceph.pwd=thecloud2015.1
#ceph.pwd=root

ceph.ip=***************
ceph.nodename=root
ceph.pwd=!QAZ2wsx
ceph.fullip=https://${ceph.ip}:8443
ceph.tokenurl=${ceph.fullip}/api/auth
ceph.inventoryurl=${ceph.fullip}/api/orchestrator/inventory
ceph.osdUrl=${ceph.fullip}/api/osd
ceph.hostUrl=${ceph.fullip}/api/host
ceph.poolUrl=${ceph.fullip}/ui-api/pool/info
ceph.nameSpacelUrl=${ceph.fullip}/api/block/pool/
ceph.healthfullUrl=${ceph.fullip}/api/health/full
ceph.smartUrl=${ceph.fullip}/api/osd/
ceph.crush_rule=${ceph.fullip}/api/crush_rule/



##solr
#spring.data.solr.host=http://***************:8983/solr
#spring.data.solr.core=doc-core