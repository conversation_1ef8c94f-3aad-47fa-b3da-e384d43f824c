---
- name: deploy theddb
  hosts: managers
  vars:
    src_docker_compose: docker-compose.yml
    dest_docker_compose: /etc/thestack/compose/docker-compose-mariadb.yml
    stack_name: theddb
    is_remove: "{{ remove }}"
  tasks:

    - name: Print install_status
      debug:
        msg: "{{ is_remove }}"

    - name: install theddb
      include_role:
        name: mariadb
      when:  is_remove == 'false'

    - name: uninstall theddb
      shell: docker stack rm theddb
      when: "is_remove == 'true'"
      run_once: true  
