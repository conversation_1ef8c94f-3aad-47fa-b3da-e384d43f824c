[Unit]
Description=the-node-exporter

After=network-online.target local-fs.target time-sync.target
Wants=network-online.target local-fs.target time-sync.target


[Service]
LimitNOFILE=1048576
LimitNPROC=1048576
EnvironmentFile=-/etc/environment
ExecStartPre=-/usr/bin/docker rm the-node-exporter
ExecStart=-/usr/bin/docker run -p 9100:9100  --network host --name the-node-exporter -t tianwen1:5000/node-exporter:latest
ExecStop=-/usr/bin/docker stop the-node-exporter
KillMode=none
Restart=on-failure
RestartSec=10s
TimeoutStartSec=120
TimeoutStopSec=120
StartLimitInterval=30min
StartLimitBurst=5

[Install]
WantedBy=default.target
