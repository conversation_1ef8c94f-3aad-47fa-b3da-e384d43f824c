---
- name: deploy mariadb
  hosts: deploy
  vars:
    src_docker_compose: docker-compose.yml
    dest_docker_compose: /etc/thestack/compose/docker-compose-mariadb.yml
    src_mariadb_conf: galera.cnf.j2
    dest_mariadb_conf: /etc/thestack/conf/mariadb/galera.cnf
    stack_name: mariadb
    is_remove: "{{ remove }}"
  tasks:

    - name: Print install_status
      debug:
        msg: "{{ is_remove }}"

    - name: install mariadb
      include_role:
        name: "../roles/mariadb-3node"
      when:  is_remove == 'false'

    - name: uninstall mariadb
      shell: docker stack rm mariadb
      when: "is_remove == 'true'"
      run_once: true  
