---
- name: mkdir -p /etc/kolla/config
  shell: mkdir -p /etc/kolla/config/glance /etc/kolla/config/nova /etc/kolla/config/cinder/cinder-volume /etc/kolla/config/cinder/cinder-backup


- name: Copy ceph Conf and Keyring
  fetch:
    src:  "{{ item.src }}"
    dest: "{{ item.dest }}"
    flat: yes
  run_once: true
  loop:
    - { src: "/etc/ceph/ceph.conf" , dest: "/etc/kolla/config/cinder/" }
    - { src: "/etc/ceph/ceph.conf" , dest: "/etc/kolla/config/glance/" }
    - { src: "/etc/ceph/ceph.conf" , dest: "/etc/kolla/config/nova/" }
    - { src: "/etc/ceph/ceph.client.glance.keyring" , dest: "/etc/kolla/config/glance/" }
    - { src: "/etc/ceph/ceph.client.cinder.keyring" , dest: "/etc/kolla/config/nova/" }
    - { src: "/etc/ceph/ceph.client.cinder.keyring" , dest: "/etc/kolla/config/cinder/cinder-volume/" }
    - { src: "/etc/ceph/ceph.client.admin.keyring" , dest: "/etc/kolla/config/cinder/cinder-volume/" }
    - { src: "/etc/ceph/ceph.client.cinder-backup.keyring" , dest: "/etc/kolla/config/cinder/cinder-backup/" }



# ---
# - name: Ensure directories exist
#   file:
#     path: /etc/kolla/config/{{ item }}
#     state: directory
#   loop:
#     - glance
#     - nova
#     - cinder/cinder-volume
#     - cinder/cinder-backup

# - name: Copy ceph Conf and Keyring
#   copy:
#     src: "{{ item.src }}"
#     dest: "{{ item.dest }}"
#     remote_src: yes  # 指定文件在远程主机上
#     mode: 0644  # 设置文件权限
#   loop:
#     - { src: "/etc/ceph/ceph.conf", dest: "/etc/kolla/config/glance/ceph.conf" }
#     - { src: "/etc/ceph/ceph.conf", dest: "/etc/kolla/config/nova/ceph.conf" }
#     - { src: "/etc/ceph/ceph.conf", dest: "/etc/kolla/config/cinder/cinder-volume/ceph.conf" }
#     - { src: "/etc/ceph/ceph.client.glance.keyring", dest: "/etc/kolla/config/glance/ceph.client.glance.keyring" }
#     - { src: "/etc/ceph/ceph.client.cinder.keyring", dest: "/etc/kolla/config/nova/ceph.client.cinder.keyring" }
#     - { src: "/etc/ceph/ceph.client.cinder.keyring", dest: "/etc/kolla/config/cinder/cinder-volume/ceph.client.cinder.keyring" }
#     - { src: "/etc/ceph/ceph.client.admin.keyring", dest: "/etc/kolla/config/cinder/cinder-volume/ceph.client.admin.keyring" }
#     - { src: "/etc/ceph/ceph.client.cinder-backup.keyring", dest: "/etc/kolla/config/cinder/cinder-backup/ceph.client.cinder-backup.keyring" }
