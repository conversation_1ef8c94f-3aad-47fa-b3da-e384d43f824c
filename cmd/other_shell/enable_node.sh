#!/bin/bash

function join_swarm() {

  # 获取当前主机名
  current_hostname=$(hostname)

  # 定义一个前缀，这个前缀与主机名相同
  prefix=${current_hostname%[1-3]*}

  # 初始化计数器
  count=1

  # 生成新的字符串，确保新字符串不等于当前主机名
  while true; do
      new_hostname="${prefix}${count}"

      # 检查新主机名是否与当前主机名相同，如果不同则退出循环
      if [ "$new_hostname" != "$current_hostname" ]; then
          break
      fi

      # 增加计数器
      count=$((count + 1))
      count=$((count > 3 ? 1 : count))
  done


  # 定义要查找的主机名
  cluster_manager_hostname=$new_hostname

  # 使用grep和awk来提取cluster_manager_ip和new_node_ip
  cluster_manager_ip=$(grep "$cluster_manager_hostname" /etc/hosts | awk '{print $1}')
  cluster_new_ip=$(grep "$current_hostname" /etc/hosts | awk '{print $1}')


  # 打印提取的IP地址
  echo "Cluster Manager IP: $cluster_manager_ip"
  echo "Cluster Manager IP: $cluster_new_ip"

  ssh_command="docker node rm $current_hostname"

  ssh $new_hostname "$ssh_command"

  ssh_command1="docker swarm join-token --quiet manager"

  token=$(ssh  $new_hostname "$ssh_command1")

  docker swarm join --advertise-addr $cluster_new_ip:2377 --token $token $cluster_manager_ip:2377

}

function enable_nova() {
  docker ps -a | grep kolla | awk '{ print $1 }' | xargs docker start
}


function enable_ceph() {
  ceph osd unset norebalance
  ceph osd unset norecover
  ceph osd unset nobackfill
}

function main() {
  if [ $# -lt 1 ]; then
    echo "Usage: $0 <node>"
    node=$HOSTNAME
  else
    node=$1
  fi

  join_swarm 
  enable_nova $node
  enable_ceph $node
}

main "$@"