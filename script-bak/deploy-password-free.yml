
---
- name: set password-free
  hosts: managers
  become: true
  vars:
    remote_username: root
  tasks:
    - name: Check if SSH key pair exists
      stat:
        path: ~/.ssh/id_rsa
      register: ssh_key_status

    - name: Generate SSH key pair if not exist
      #run_once: true
      command: ssh-keygen -t rsa -b 4096 -N '' -f ~/.ssh/id_rsa
      args:
        creates: ~/.ssh/id_rsa
      when: not ssh_key_status.stat.exists

    - name: Set correct permissions on .ssh directory
      file:
        path: "/root/.ssh"
        state: directory
        mode: "0700"
      loop: "{{ groups['managers'] }}"

    - name: Set correct permissions on authorized_keys file
      file:
        path: "/root/.ssh/authorized_keys"
        state: touch
        mode: "0600"
      loop: "{{ groups['managers'] }}"

    - name: Check if key is already installed
      ansible.builtin.command: ssh -o BatchMode=yes root@{{ item }} "grep -q $(cat ~/.ssh/id_rsa.pub) ~/.ssh/authorized_keys"
      register: result
      ignore_errors: true
      loop: "{{ groups['managers'] }}"
      become: true

    - name: Disable strict host key checking
      ansible.builtin.lineinfile:
        path: /etc/ssh/ssh_config
        line: 'StrictHostKeyChecking no'
        state: present
      become: yes
  
    - name: Copy public key to remote hosts
      ansible.builtin.shell: "sshpass -p '{{ hostvars[item]['ansible_ssh_pass'] }}' ssh-copy-id {{ remote_username }}@{{ item }}"
      when: result is failed #  and ansible_facts['fqdn'] != item
      loop: "{{ groups['managers'] }}"
      become: true

