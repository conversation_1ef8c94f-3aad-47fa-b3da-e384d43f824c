global
    log stdout local0
    maxconn 4096

defaults
    log global
    mode http
    timeout connect 5000ms
    timeout client 50000ms
    timeout server 50000ms

listen status
    bind 192.168.214.105:8932
    mode http
    stats enable
    stats uri /
    stats refresh 15s
    stats realm Haproxy\ Stats
    stats auth admin:admin

listen cephapi
    mode http
    bind 192.168.214.105:8443
    capture cookie vgnvisitor= len 32
    cookie SERVERID insert indirect nocache
    option forwardfor
    mode http
    option forwardfor
    option httpchk  GET /
    http-check expect status 200
    option httpclose
    server controller1 192.168.214.101:8443 cookie controller1 check inter 2000 rise 2 fall 5
    server controller2 192.168.214.102:8443 cookie controller2 check inter 2000 rise 2 fall 5
    server controller3 192.168.214.103:8443 cookie controller3 check inter 2000 rise 2 fall 5

    