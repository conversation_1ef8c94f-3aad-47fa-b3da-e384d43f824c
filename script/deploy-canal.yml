---

- name: upload config canal
  hosts: managers
  vars:
    src_docker_compose: docker-compose.yml
    dest_docker_compose: /etc/thestack/compose/docker-compose-canal.yml

    stack_name: canal
    is_remove: "{{ remove }}"
  tasks:
    - name: Create multiple directories
      file:
        path: "/etc/thestack/conf/{{ item }}"
        state: directory 
        mode: '0755'
        recurse: yes
      with_items:
        - canal

    - name: Upload configuration files from templates
      template:
        src: "{{ playbook_dir }}/../roles/canal/templates/{{ item.dir }}/{{ item.file }}"
        dest: "/etc/thestack/conf/{{ item.dir }}/{{ item.file }}"
        force: yes
        mode: '0644'
        backup: yes
      loop:
        - { dir: 'canal', file: 'instance.properties' }


- name: deploy canal
  hosts: deploy
  vars:
    src_docker_compose: docker-compose.yml
    dest_docker_compose: /etc/thestack/compose/docker-compose-canal.yml

    stack_name: canal
    is_remove: "{{ remove }}"
  tasks:

    - name: Print install_status
      debug:
        msg: "{{ is_remove }}"

    - name: install canal
      include_role:
        name: "../roles/canal"
      when:  is_remove == 'false'

    - name: uninstall canal
      shell: docker stack rm canal
      when: "is_remove == 'true'"
      run_once: true

