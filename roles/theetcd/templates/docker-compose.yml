version: '3.8'

services:
  theetcd:
    image: tianwen1:5000/coreos/etcd:v3.5.12
    configs:
      - source: theetcd_conf
        target: /var/lib/etcd/conf/etcd.conf.yml
    networks:
      - traefik_prod

    command: /usr/local/bin/etcd --config-file=/var/lib/etcd/conf/etcd.conf.yml
    volumes:
      - theetcddb:/var/etcd
      - "/etc/localtime:/etc/localtime:ro"
    deploy:
      mode: replicated
      replicas: 1
      restart_policy:
        condition: any
        delay: 10s
        max_attempts: 3
        window: 180s
      placement:
        constraints:
          - node.role==manager
      labels:
        - traefik.enable=true
        - traefik.docker.network=traefik_prod
        - traefik.tcp.routers.theetcd.rule=HostSNI(`*`)
        - traefik.tcp.routers.theetcd.entrypoints=theetcd
        - traefik.tcp.routers.theetcd.tls=false
        - traefik.tcp.routers.theetcd.service=theetcd
        - traefik.tcp.routers.theetcd-atl.rule=HostSNI(`*`)
        - traefik.tcp.routers.theetcd-atl.entrypoints=theetcd-atl
        - traefik.tcp.routers.theetcd-atl.tls=false
        - traefik.tcp.routers.theetcd-atl.service=theetcd
        - traefik.tcp.services.theetcd.loadbalancer.server.port=2379


networks:
  traefik_prod:
    external: true

configs:
  theetcd_conf:
    file: /etc/thestack/conf/etcd/etcd.conf.yml

volumes:
  theetcddb:
    external: true
