# 守护进程启动,docker方式需要以前台的方式启动
daemonize no

# pid
pidfile /var/run/redis_6379.pid

# redis 端口
port 6379

# 允许访问redis的ip
bind 0.0.0.0

# 关闭无消息的客户端间隔
timeout 0

# 对客户端发送ack信息 单位秒
tcp-keepalive 0

# 数据库数量，暂时设定四个
databases 8

# redis 持久化
# save 60 1  1分钟后并且至少有一次更新
# save 10 1

# 持久化失败redis是否停止运行
# stop-writes-on-bgsave-error no

# 持久化的时候是否运行对字符串对象进行压缩，算法为LZF
# rdbcompression yes


# 持久化的时候是否运行对字符串对象进行压缩，算法为LZF
# rdbcompression yes

# 文件末尾是否包含一个CRC64的校验和
# rdbchecksum yes

# redis存储数据的文件，注意多实例的时候该不同名字或者用不同的工作目录
# dbfilename dump.rdb

# redis 工作目录
dir /usr/local/redis/data

# redis 允许同时最多链接数量
maxclients 65535

# 设置redis 的内存上限，我这个机器只有1GB，而且需要缓存的东西不多设置 128MB 先
maxmemory 128mb


# 内存策略：如果达到内存限制了，Redis如何删除key。你可以在下面五个策略里面选：
#
# volatile-lru -> 根据LRU算法生成的过期时间来删除。
# allkeys-lru -> 根据LRU算法删除任何key。
# volatile-random -> 根据过期设置来随机删除key。
# allkeys->random -> 无差别随机删。
# volatile-ttl -> 根据最近过期时间来删除（辅以TTL）
# noeviction -> 谁也不删，直接在写操作时返回错误。
#
# 注意：对所有策略来说，如果Redis找不到合适的可以删除的key都会在写操作时返回一个错误。
#
#     这里涉及的命令：set setnx setex append
#     incr decr rpush lpush rpushx lpushx linsert lset rpoplpush sadd
#     sinter sinterstore sunion sunionstore sdiff sdiffstore zadd zincrby
#     zunionstore zinterstore hset hsetnx hmset hincrby incrby decrby
#     getset mset msetnx exec sort
#
#
maxmemory-policy volatile-lru

# LRU和最小TTL算法的实现都不是很精确，但是很接近（为了省内存），所以你可以用样例做测试。
# 例如：默认Redis会检查三个key然后取最旧的那个，你可以通过下面的配置项来设置样本的个数。
#
# maxmemory-samples 3

################################################################################
#                               redis 的累加模式                                 #
################################################################################

# 默认情况下，Redis是异步的把数据导出到磁盘上。这种情况下，当Redis挂掉的时候，最新的数据就丢了。
# 如果不希望丢掉任何一条数据的话就该用纯累加模式：一旦开启这个模式，Redis会把每次写入的数据在接收
# 后都写入 appendonly.aof 文件。
# 每次启动时Redis都会把这个文件的数据读入内存里。
#
# 注意，异步导出的数据库文件和纯累加文件可以并存（你得把上面所有"save"设置都注释掉，关掉导出机制）。
# 如果纯累加模式开启了，那么Redis会在启动时载入日志文件而忽略导出的 dump.rdb 文件。
#
# 重要：查看 BGREWRITEAOF 来了解当累加日志文件太大了之后，怎么在后台重新处理这个日志文件。
appendonly yes

# 纯累加文件名字（默认："appendonly.aof"）
appendfilename appendonly.aof

# 纯累加文件的flush频率
# always    ->  每次写入都flush，最安全，资源开销最大
# everysec  ->  每秒 (推荐)
# no        ->  由系统确定

# appendfsync always
appendfsync everysec
# appendfsync no

# 当纯累加文件进行rewrite时，是否需要fsync
# 当且仅当appendfsync = always || everysec 时该参数生效
no-appendfsync-on-rewrite no

# 纯累加文件下次rewrite的比例，与纯累加文件文件的最小size
# 下面的参数意味着纯累加文件会在512mb的时候进行一次rewrite
# 若rewrite后的文件大小为x mb，则下次纯累加文件将会在2x mb时rewrite
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 512mb

################################################################################
#                                redis 的高级配置                                #
################################################################################

# 如果hash中的数量超出hash-max-ziplist-entries，或者value的长度超出
# hash-max-ziplist-value，将改成保存dict，否则以ziphash的方式存储以节省空间。以下同理。
hash-max-ziplist-entries 64
hash-max-ziplist-value 128

list-max-ziplist-entries 64
list-max-ziplist-value 128

set-max-intset-entries 64

zset-max-ziplist-entries 64
zset-max-ziplist-value 128

# 是否resize hash? 如果你设置成no需要在源码做一定的修改以防止有人进行hash攻击
activerehashing yes



######################## 日志设置

# 日志等级 debug, verbose, notice, warning。生产环境建议用notice
loglevel notice

# 日志输出的文件名
logfile /var/log/redis/redis.log

# 是否将日志写入系统日志，默认为no，建议为no
# syslog-enabled no

# 在系统日志中的标识
# syslog-ident redis

# 写到哪个系统日志中 USER或者LOCAL0-LOCAL7.
# syslog-facility local0

######################### 密码设置
# requirepass 123456