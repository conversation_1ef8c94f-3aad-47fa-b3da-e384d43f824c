---
- name: deploy thecephapi
  hosts: managers
  vars:
    src_docker_compose: docker-compose.yml
    dest_docker_compose: /etc/thestack/compose/docker-compose-thecephapi.yml
    stack_name: thecephapi
    is_remove: "{{ remove }}"
  tasks:

    - name: Print install_status
      debug:
        msg: "{{ is_remove }}"

    - name: install thecephapi
      include_role:
        name: thecephapi
      when:  is_remove == 'false'

    - name: uninstall thecephapi
      shell: docker stack rm thecephapi
      when: "is_remove == 'true'"
      run_once: true  
