---
- name: deploy healthcheck
  hosts: managers
  vars:
    src_docker_compose: docker-compose.yml
    dest_docker_compose: /etc/thestack/compose/docker-compose-healthcheck.yml
    stack_name: healthcheck
    is_remove: "{{ remove }}"
  tasks:

    - name: Print install_status
      debug:
        msg: "{{ is_remove }}"

    - name: install healthcheck
      include_role:
        name: healthcheck
      when:  is_remove == 'false'

    - name: uninstall healthcheck
      shell: docker stack rm healthcheck
      when: "is_remove == 'true'"
      run_once: true  
