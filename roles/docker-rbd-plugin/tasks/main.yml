---
- name: install docker rbd plugin
  shell: rbd config global set global rbd_default_features 1
  when: "inventory_hostname == 'controller1'"  

- name: Check if docker plugin is installed
  ansible.builtin.command: docker plugin inspect tianwen1:5000/the/rbd:latest 
  register: docker_plugin_check
  ignore_errors: true

- name: install docker rbd plugin
  shell: docker plugin enable tianwen1:5000/the/rbd:latest 
  ignore_errors: true

- name: install docker rbd plugin
  shell: docker plugin install --grant-all-permissions  tianwen1:5000/the/rbd:latest  LOG_LEVEL=3 RBD_CONF_POOL="therbd" RBD_CONF_CLUSTER=ceph RBD_CONF_KEYRING_USER=client.the
  when: docker_plugin_check.rc != 0

- name: Pause for 10 seconds to wait for rbd plugin
  pause:
    seconds: 20


- name: create  docker volume for thedb
  shell: docker volume create -d tianwen1:5000/the/rbd:latest -o size=5000 thedb
  run_once: true
  
- name: create  docker volume for the_kingbase
  shell: docker volume create -d tianwen1:5000/the/rbd:latest -o size=5000 thekingbase
  when: kingbase is defined and kingbase
  
- name: create  docker volume for prometheus
  shell: docker volume create -d tianwen1:5000/the/rbd:latest -o size=5000 theprometheus
  run_once: true

#- name: create  docker volume for alertmanager
#  shell: docker volume create -d tianwen1:5000/the/rbd:latest -o size=5000 thealertmanager
#  run_once: true

- name: create  docker volume for es
  shell: docker volume create -d tianwen1:5000/the/rbd:latest -o size=5000 -o uid=1000 theesdata
  run_once: true

- name: create  docker volume for  grafana
  shell: docker volume create -d tianwen1:5000/the/rbd:latest -o size=5000 -o uid=472 thegrafana
  run_once: true

- name: create  docker volume for  theetcddb
  shell: docker volume create -d tianwen1:5000/the/rbd:latest -o size=5000 -o uid=472 theetcddb
  run_once: true
  
- name: modify plugin dir
  shell: chmod 777 -R /var/lib/docker/plugins/

- name: create the log dir
  shell: mkdir -p /var/log/the




