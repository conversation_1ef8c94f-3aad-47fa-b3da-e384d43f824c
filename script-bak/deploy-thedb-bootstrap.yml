---
- name: deploy thedb-bootstrap
  hosts: managers
  vars:
    src_docker_compose: docker-compose.yml
    dest_docker_compose: /etc/thestack/compose/docker-compose-thedb-bootstrap.yml
    stack_name: thedb-bootstrap
    is_remove: "{{ remove }}"
  tasks:

    - name: Print install_status
      debug:
        msg: "{{ is_remove }}"

    - name: install thedb-bootstrap
      include_role:
        name: thedb-bootstrap
      when:  is_remove == 'false'

    - name: uninstall thedb-bootstrap
      shell: docker stack rm thedb-bootstrap
      when: "is_remove == 'true'"
      run_once: true  
