---
- name: Remove existing stack
  shell: docker stack rm {{ stack_name }}
  run_once: true
  ignore_errors: yes
  when: inventory_hostname == 'controller1'

- name: Wait for stack to be removed
  pause:
    seconds: 20
  run_once: true

- name: Remove Galera state files on controller1
  file:
    path: "{{ item }}"
    state: absent
  with_items:
    - "/var/lib/docker/volumes/mariadb_db_data_1/_data/gvwstate.dat"
    - "/var/lib/docker/volumes/mariadb_db_data_1/_data/grastate.dat"
    - "/var/lib/docker/volumes/mariadb_db_data_1/_data/galera.cache"
  when: inventory_hostname == 'controller1'


- name: Create grastate.dat on controller1
  block:
    - name: Ensure directory exists
      file:
        path: "/var/lib/docker/volumes/hci_db_data_1/_data"
        state: directory
        mode: '0755'
        recurse: yes
    - name: Create grastate.dat
      copy:
        dest: "/var/lib/docker/volumes/hci_db_data_1/_data/grastate.dat"
        content: |
          # GALERA saved state
          version: 2.1
          uuid: 00000000-0000-0000-0000-000000000000
          seqno: -1
          safe_to_bootstrap: 1
        mode: '0644'
  when: inventory_hostname == 'controller1'


- name: Remove and create Galera state files on controller2
  block:
    - name: Remove old files
      file:
        path: "{{ item }}"
        state: absent
      with_items:
        - "/var/lib/docker/volumes/mariadb_db_data_2/_data/gv*state.dat"
        - "/var/lib/docker/volumes/mariadb_db_data_2/_data/galera.cache"
    
    - name: Create grastate.dat
      copy:
        dest: "/var/lib/docker/volumes/mariadb_db_data_2/_data/grastate.dat"
        content: |
          # GALERA saved state
          version: 2.1
          uuid: 00000000-0000-0000-0000-000000000000
          seqno: -1
          safe_to_bootstrap: 0
        mode: '0644'
  when: inventory_hostname == 'controller2'

- name: Remove and create Galera state files on controller3
  block:
    - name: Remove old files
      file:
        path: "{{ item }}"
        state: absent
      with_items:
        - "/var/lib/docker/volumes/mariadb_db_data_3/_data/gv*state.dat"
        - "/var/lib/docker/volumes/mariadb_db_data_3/_data/galera.cache"
    
    - name: Create grastate.dat
      copy:
        dest: "/var/lib/docker/volumes/mariadb_db_data_3/_data/grastate.dat"
        content: |
          # GALERA saved state
          version: 2.1
          uuid: 00000000-0000-0000-0000-000000000000
          seqno: -1
          safe_to_bootstrap: 0
        mode: '0644'
  when: inventory_hostname == 'controller3'

- name: Upload docker compose file
  template:
    src: "{{ src_docker_compose }}"
    dest: "{{ dest_docker_compose }}"
  when: inventory_hostname == 'controller1'

- name: Deploy services
  shell: docker stack deploy -c {{ dest_docker_compose }} {{ stack_name }}
  run_once: true
  when: inventory_hostname == 'controller1'

- name: Pause for 10 seconds to wait for services being running
  pause:
    seconds: 10
  run_once: true
  when: inventory_hostname == 'controller1'

- name: Ensure services deployed
  shell: docker service ls
  register: service_output
  when: inventory_hostname == 'controller1'

- name: Output service list
  debug:
    msg: "{{ service_output.stdout_lines }}"
  when: inventory_hostname == 'controller1'

- name: Show stacks
  shell: docker stack list
  run_once: true
  when: inventory_hostname == 'controller1'