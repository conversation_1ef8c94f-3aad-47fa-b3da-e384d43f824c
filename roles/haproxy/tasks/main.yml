---
- name: Upload docker compose file
  template:
    src: "{{ src_docker_compose }}"
    dest: "{{ dest_docker_compose }}"
    force: yes
    mode: '0644'    # 可选：设置文件权限
    backup: yes     # 可选：在覆盖前创建备份

- name: Ensure haproxy config directory exists
  file:
    path: "{{ dest_haproxy_conf | dirname }}"
    state: directory
    mode: '0755'
    recurse: yes

- name: upload haproxy conf
  template:
    src: "{{ src_haproxy_conf }}"
    dest: "{{ dest_haproxy_conf }}"
    force: yes
    mode: '0644'    # 可选：设置文件权限
    backup: yes     # 可选：在覆盖前创建备份

- name: Deploy services
  shell: docker stack deploy -c {{ dest_docker_compose }} {{ stack_name }}
  run_once: true

- name: Pause for 2 seconds to wait for services being running
  pause:
    seconds: 2
  run_once: true

- name: Ensure services deployed
  shell: docker service ls
  register: service_output

- name: Output service list
  debug:
    msg: "{{ service_output.stdout_lines }}"

- name: Show stacks
  shell: docker stack list
