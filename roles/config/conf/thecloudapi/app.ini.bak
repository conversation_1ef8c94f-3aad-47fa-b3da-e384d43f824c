[app]
PageSize = 10
JwtSecret = 233
PrefixUrl = http://127.0.0.1:8082

RuntimeRootPath = runtime/

ImageSavePath = upload/images/
# MB
ImageMaxSize = 5
ImageAllowExts = .jpg,.jpeg,.png

ExportSavePath = export/
QrCodeSavePath = qrcode/
FontSavePath = fonts/

LogSavePath = logs/
LogSaveName = log
LogFileExt = log
TimeFormat = 20060102

[server]
#debug or release
RunMode = debug
HttpPort = 8007
VdiPort = 8082
ReadTimeout = 60
WriteTimeout = 60

[database]
Type = mysql
User = root
Password = thecloud2015.1
Host = db:3306
Name = the
TablePrefix =

[thedeskserver]
# 访问云桌面项目的其他服务
DeskHost = http://thedesk
DeskPort = 8085

[etcd]
EtcdUrl = {{ vip }}:2379

[keystone]
AuthUri = http://{{ vip }}:5000/v3
AuthType = password
ProjectDomainId = default
UserDomainId = default
ProjectName = admin
Username = admin
Password = thecloud2015.1

; [redis]
; Host = 127.0.0.1:6379
; Password =
; MaxIdle = 30
; MaxActive = 30
; IdleTimeout = 200
