tcp:
  routers:
    keystone-router:
      rule: "HostSNI(`*`)"
      service: keystone-service
      entryPoints:
        - keystone
    memcached-router:
      rule: "HostSNI(`*`)"
      service: memcached-service
      entryPoints:
        - memcached
    glance-api-router:
      rule: "HostSNI(`*`)"
      service: glance-api-service
      entryPoints:
        - glance-api
    placement-router:
      rule: "HostSNI(`*`)"
      service: placement-service
      entryPoints:
        - placement
    nova-api-router:
      rule: "HostSNI(`*`)"
      service: nova-api-service
      entryPoints:
        - nova-api
    nova-metadata-api-router:
      rule: "HostSNI(`*`)"
      service: nova-metadata-api-service
      entryPoints:
        - nova-metadata-api
    nova-novncproxy-router:
      rule: "HostSNI(`*`)"
      service: nova-novncproxy-service
      entryPoints:
        - nova-novncproxy
    neutron-server-router:
      rule: "HostSNI(`*`)"
      service: neutron-server-service
      entryPoints:
        - neutron-server
    cinder-api-router:
      rule: "HostSNI(`*`)"
      service: cinder-api-service
      entryPoints:
        - cinder-api
    keystone-admin-router:
      rule: "HostSNI(`*`)"
      service: keystone-admin-service
      entryPoints:
        - keystone-admin

  services:
    keystone-service:
      loadBalancer:
        servers:
          - address: "***************:5000"
          - address: "***************:5000"
          - address: "***************:5000"

            
    memcached-service:
      loadBalancer:
        servers:
          - address: "***************:11211"
          - address: "***************:11211"
          - address: "***************:11211"

            
    glance-api-service:
      loadBalancer:
        servers:
          - address: "***************:9292"
          - address: "***************:9292"
          - address: "***************:9292"

            
    placement-service:
      loadBalancer:
        servers:
          - address: "***************:8778"
          - address: "***************:8778"
          - address: "***************:8778"

            
    nova-api-service:
      loadBalancer:
        servers:
          - address: "***************:8774"
          - address: "***************:8774"
          - address: "***************:8774"

            
    nova-metadata-api-service:
      loadBalancer:
        servers:
          - address: "***************:8775"
          - address: "***************:8775"
          - address: "***************:8775"

            
    nova-novncproxy-service:
      loadBalancer:
        servers:
          - address: "***************:6080"
          - address: "***************:6080"
          - address: "***************:6080"

            
    neutron-server-service:
      loadBalancer:
        servers:
          - address: "***************:9696"
          - address: "***************:9696"
          - address: "***************:9696"

            
    cinder-api-service:
      loadBalancer:
        servers:
          - address: "***************:8776"
          - address: "***************:8776"
          - address: "***************:8776"

                   
    keystone-admin-service:
      loadBalancer:
        servers:
          - address: "***************:35357"
          - address: "***************:35357"
          - address: "***************:35357"

            
          
          
          
