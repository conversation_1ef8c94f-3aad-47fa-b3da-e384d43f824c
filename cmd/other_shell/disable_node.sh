#!/bin/bash
  
function leave_swarm() {
  echo "Method 1 called with argument: $node"

        # 清空节点的docker容器
        docker node update --availability pause $node

        if [ $? -ne 0 ]; then
          echo "Failed to del docker $node "
          exit 1
        fi
  echo "Method 1 called success to del  docker: $node"

        docker node demote $node
        docker swarm leave --force

        if [ $? -ne 0 ]; then
          echo "Failed to leave swarm $node "
          exit 1
        fi
  echo "succsess to leave swarm $node "

}

function disable_nova() {
  echo " to disable_nova "
  docker ps -a | grep kolla | awk '{ print $1 }' | xargs docker stop
}

function disable_ceph(){
  echo "disbale_ceph  to leave swarm"
  ceph osd set norebalance
  ceph osd set norecover
  ceph osd set nobackfill
}

function main() {
  sleep 2
  node=$HOSTNAME
  echo $node
  disable_nova $node
  disable_ceph $node
  leave_swarm $node

}

main "$@"