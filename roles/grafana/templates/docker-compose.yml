version: "3.7"
services:
  grafana:
    image: tianwen1:5000/grafana:latest
    hostname: grafana
    networks:
      - prod
    volumes:
      - thegrafana:/var/lib/grafana
    configs:
      - source: grafana.ini
        target: /etc/grafana/grafana.ini
      - source: grafana.crt
        target: /etc/grafana/grafana.crt
      - source: grafana.key
        target: /etc/grafana/grafana.key

    deploy:
      mode: replicated
      restart_policy:
        condition: any
        delay: 10s
        max_attempts: 3
        window: 180s
      placement:
        constraints:
          - node.role==manager
      labels:
        - traefik.enable=true
        - traefik.docker.network=traefik_prod
        - traefik.tcp.routers.grafana.rule=HostSNI(`*`)
        - traefik.tcp.routers.grafana.entrypoints=grafana
        - traefik.tcp.routers.grafana.tls=false
        - traefik.tcp.routers.grafana.service=grafana
        - traefik.tcp.services.grafana.loadbalancer.server.port=3000


configs:
  grafana.ini:
    file: /etc/thestack/conf/grafana/grafana.ini
  grafana.crt:
    file: /etc/thestack/conf/traefik/yourdomain.crt
  grafana.key:
    file: /etc/thestack/conf/traefik/yourdomain.key


volumes:
  thegrafana:
    external: true

networks:
  prod:
    external: true
