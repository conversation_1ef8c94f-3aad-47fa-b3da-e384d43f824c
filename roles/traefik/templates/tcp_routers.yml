tcp:
  services:
    mariadb-service:
      loadBalancer:
        servers:
          - address: "mariadb-1:3306"
          - address: "mariadb-2:3306"
          - address: "mariadb-3:3306"
    rabbitmq-service:
      loadBalancer:
        servers:
          - address: "rabbitmq1:5672"
          - address: "rabbitmq2:5672"
          - address: "rabbitmq3:5672"
    mgr-service:
      loadBalancer:
        servers:
          - address: "mgr:8886"
  routers:
    mariadb:
      entryPoints:
        - mariadb
      rule: "HostSNI(`*`)"
      service: mariadb-service
    rabbitmq:
      entryPoints:
        - rabbitmq
      rule: "HostSNI(`*`)"
      service: rabbitmq-service
    mgr:
      entryPoints:
        - mgr
      rule: "HostSNI(`*`)"
      service: mgr-service