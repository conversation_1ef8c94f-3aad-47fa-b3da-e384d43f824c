global:
  resolve_timeout: 5m
  smtp_from: '<EMAIL>'
  smtp_smarthost: 'smtp.exmail.qq.com:465'
  smtp_auth_username: '<EMAIL>'
  smtp_auth_password: 'Higgsmaojj001'
  smtp_require_tls: false
  smtp_hello: 'qq.com'
templates:
  - '/etc/alertmanager/template/*.tmpl'

route:
  group_by: ['testrule']
  group_wait: 30s
  group_interval: 5m
  repeat_interval: 1h
  receiver: 'email'
receivers:
  - name: 'email'
    email_configs:
      - to: '<EMAIL>'
        send_resolved: true
        headers:
          subject: '{{ template "custom_mail_subject" . }}'
        html: '{{ template "email_html" . }}'
  - name: 'web.hook'
    webhook_configs:
      - url: 'http://127.0.0.1:5000/'
inhibit_rules:
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'dev', 'instance']
