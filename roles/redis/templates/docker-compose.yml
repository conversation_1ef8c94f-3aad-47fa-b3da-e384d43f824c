version: "3.7"

services:
  redis:
    image: 'tianwen1:5000/redis:latest'
    #restart: always
    hostname: redis
    #ports:
    #  - 6379:6379
    networks:
      - prod
    volumes:
      - "theredisdata:/data"
      # - "theredislog:/var/log/redis"
      - /usr/share/zoneinfo/Asia/Shanghai:/etc/localtime
    # command: ["/etc/redis/redis.conf"]
    # configs:
    #   - source: theconf_redis.conf
    #     target: /etc/redis/redis.conf
    deploy:
      mode: replicated
      replicas: 1
      restart_policy:
        condition: any
        delay: 10s
        window: 120s
      placement:
        constraints:
          - node.role==manager

#configs:
#  theconf_redis.conf:
#    external: true


networks:
  prod:
    external: true

volumes:
 theredisdata:
#  theredislog:

#volumes:
#  theredisdata:
#    external: true
#  theredislog:
#    external: true


