version: "3.7"
services:
  thewebvue:
    image: tianwen1:5000/thewebvue:latest
    networks:
      - traefik_prod
    deploy:
      replicas: 2
      labels:
        - traefik.enable=true
        - traefik.http.middlewares.thewebvue_strip.stripprefix.prefixes=/
        - traefik.http.routers.thewebvue.rule=PathPrefix(`/`)
        - traefik.http.routers.thewebvue.entrypoints=web
        - traefik.http.services.thewebvue.loadbalancer.server.port=8066
        - traefik.http.routers.thewebvue.middlewares=thewebvue_strip
        - traefik.docker.network=traefik_prod

  theauth:
    image: tianwen1:5000/theauth:latest
    networks:
      - traefik_prod
    ports:
      - 8088:8088
    volumes:
      - /var/log/the:/var/log/the
    configs:
      - source: theconf_theauth.ini
        target: /etc/vdiauth/config.ini
    deploy:
      replicas: 1
      labels:
        - traefik.enable=true
        - traefik.http.middlewares.theauth_strip.stripprefix.prefixes=/login
        - traefik.http.routers.theauth.rule=PathPrefix(`/login`)
        - traefik.http.routers.theauth.entrypoints=web
        - traefik.http.services.theauth.loadbalancer.server.port=8088
        - traefik.http.routers.theauth.middlewares=theauth_strip
        - traefik.docker.network=traefik_prod

  theweb:
    image: tianwen1:5000/theweb:latest
    hostname: theweb
    networks:
      - traefik_prod
    ports:
      - 8006:8006
    volumes:
      - /var/log/the:/var/log/the
    configs:
      - source: theconf_theweb.py
        target: /code/settings.py
    deploy:
      replicas: 1
      labels:
        - traefik.enable=true
        - traefik.http.middlewares.theweb_strip.stripprefix.prefixes=/theapi
        - traefik.http.routers.theweb.rule=PathPrefix(`/theapi`)
        - traefik.http.routers.theweb.entrypoints=web
        - traefik.http.services.theweb.loadbalancer.server.port=8006
        - traefik.http.routers.theweb.middlewares=theweb_strip
        - traefik.docker.network=traefik_prod




  theupload:
    image: tianwen1:5000/theupload:latest
    hostname: theupload
    networks:
      - traefik_prod
    volumes:
      - /etc/ceph:/etc/ceph
      - theshare:/data/share
      - /var/log/the:/var/log/the
    configs:
      - source: theconf_theweb.py
        target: /code/settings.py
    deploy:
      replicas: 1
      labels:
        - traefik.enable=true
        - traefik.http.middlewares.theupload_strip.stripprefix.prefixes=/upload
        - traefik.http.routers.theupload.rule=PathPrefix(`/upload`)
        - traefik.http.routers.theupload.entrypoints=web
        - traefik.http.services.theupload.loadbalancer.server.port=8090
        - traefik.http.routers.theupload.middlewares=theupload_strip
        - traefik.docker.network=traefik_prod

  thevmware:
    image: tianwen1:5000/thevmware:latest
    hostname: thevmware
    networks:
      - traefik_prod
    volumes:
      - /var/log/the:/var/log/the
    configs:
      - source: theconf_theweb.py
        target: /code/settings.py
    deploy:
      replicas: 1
      labels:
        - traefik.enable=true
        - traefik.http.middlewares.thevmware_strip.stripprefix.prefixes=/cloudy
        - traefik.http.routers.thevmware.rule=PathPrefix(`/cloudy`)
        - traefik.http.routers.thevmware.entrypoints=web
        - traefik.http.services.thevmware.loadbalancer.server.port=8091
        - traefik.http.routers.thevmware.middlewares=thevmware_strip
        - traefik.docker.network=traefik_prod

  theuser:
    image: tianwen1:5000/theuser:latest
    hostname: theuser
    networks:
      - traefik_prod
    volumes:
      - /var/log/the:/var/log/the
    configs:
      - source: theconf_theweb.py
        target: /code/settings.py
    deploy:
      replicas: 1
      labels:
        - traefik.enable=true
        - traefik.http.middlewares.theuser_strip.stripprefix.prefixes=/user
        - traefik.http.routers.theuser.rule=PathPrefix(`/user`)
        - traefik.http.routers.theuser.entrypoints=web
        - traefik.http.services.theuser.loadbalancer.server.port=8089
        - traefik.http.routers.theuser.middlewares=theuser_strip
        - traefik.docker.network=traefik_prod



  thelog:
    image: tianwen1:5000/thelog:latest
    hostname: thelog
    networks:
      - traefik_prod
    volumes:
      - /var/log/the:/var/log/the
    configs:
      - source: theconf_theweb.py
        target: /code/settings.py
    deploy:
      replicas: 1
      labels:
        - traefik.enable=true
        - traefik.http.middlewares.thelog_strip.stripprefix.prefixes=/log
        - traefik.http.routers.thelog.rule=PathPrefix(`/log`)
        - traefik.http.routers.thelog.entrypoints=web
        - traefik.http.services.thelog.loadbalancer.server.port=8093
        - traefik.http.routers.thelog.middlewares=thelog_strip
        - traefik.docker.network=traefik_prod

  theac:
    image: tianwen1:5000/theac:latest
    hostname: theac
    networks:
      - traefik_prod
    volumes:
      - /var/log/the:/var/log/the
    configs:
      - source: theconf_theweb.py
        target: /code/settings.py
    deploy:
      replicas: 1
      labels:
        - traefik.enable=true
        - traefik.http.middlewares.thelog_strip.stripprefix.prefixes=/ac
        - traefik.http.routers.thelog.rule=PathPrefix(`/ac`)
        - traefik.http.routers.thelog.entrypoints=web
        - traefik.http.services.thelog.loadbalancer.server.port=8081
        - traefik.http.routers.thelog.middlewares=thelog_strip
        - traefik.docker.network=traefik_prod

volumes:
  theshare:
  
networks:
  traefik_prod:
    external: true

configs:
  theconf_theweb.py:
    external: true
  theconf_theauth.ini:
    external: true