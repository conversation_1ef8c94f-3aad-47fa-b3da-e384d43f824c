---
- name: Ensure docker compose yaml file directory exists
  file:
    path: "{{ dest_docker_compose | dirname }}"
    state: directory
    mode: '0755'
    recurse: yes


- name: Upload docker compose file
  template:
    src: "{{ src_docker_compose }}"
    dest: "{{ dest_docker_compose }}"


- name: Ensure mariadb config directory exists
  file:
    path: "{{ dest_mariadb_conf | dirname }}"
    state: directory
    mode: '0755'
    recurse: yes

- name: Upload configuration files from templates
  template:
    src: "conf/{{ item.dir }}/{{ item.file }}"
    dest: "/etc/thestack/conf/{{ item.dir }}/{{ item.file }}"
    force: yes
    mode: '0644'
    backup: yes
  loop:
    - { dir: 'mariadb', file: 'galera.cnf-1' }
    - { dir: 'mariadb', file: 'galera.cnf-2' }
    - { dir: 'mariadb', file: 'galera.cnf-3' }


- name: Add mariadb-node node labels
  shell: "docker node update --label-add mariadb-node={{ idx + 1 }} {{ item }}"
  with_items: "{{ groups['managers'] }}"
  loop_control:
    index_var: idx
  run_once: true


- name: Deploy services
  shell: docker stack deploy -c {{ dest_docker_compose }} {{ stack_name }}
  run_once: true

- name: Pause for 10 seconds to wait for services being running
  pause:
    seconds: 10
  run_once: true
  
- name: Ensure services deployed
  shell: docker service ls
  register: service_output

- name: Output service list
  debug:
    msg: "{{ service_output.stdout_lines }}"

- name: Show stacks
  shell: docker stack list