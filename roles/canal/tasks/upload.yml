- name: Create multiple directories
  file:
    path: "/etc/thestack/conf/{{ item }}"
    state: directory 
    mode: '0755'
    recurse: yes
  with_items:
    - canal

- name: Upload configuration files from templates
  template:
    src: "{{ item.dir }}/{{ item.file }}"
    dest: "/etc/thestack/conf/{{ item.dir }}/{{ item.file }}"
    force: yes
    mode: '0644'
    backup: yes
  loop:
    - { dir: 'canal', file: 'instance.properties' }