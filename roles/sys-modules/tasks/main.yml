---
- name: mkdir -p /etc/thestack/compose
  shell: mkdir -p /etc/thestack/compose

- name: chmod 777 -R /etc/thestack
  shell: chmod 777 -R /etc/thestack

# - name: Create /etc/thestack directory
#   file:
#     path: /etc/thestack
#     state: directory
- name: create prod netowrk
  shell: "modprobe ip_vs"


- name: lsmod list
  shell: "lsmod | grep ip_vs"

- name: touch /etc/sysctl.d/98-the.conf
  shell: touch /etc/sysctl.d/98-the.conf

- name: Configure Hosts File
  lineinfile: path=/etc/sysctl.d/98-the.conf regexp='.*net.ipv4.ip_forward$' line="net.ipv4.ip_forward = 1" state=present


- name: sysctl -p
  shell: "sysctl -p"

