---
- name: deploy redis
  hosts: managers
  vars:
    src_docker_compose: docker-compose.yml
    dest_docker_compose: /etc/thestack/compose/docker-compose-redis.yml
    stack_name: redis
    is_remove: "{{ remove }}"
  tasks:

    - name: Print install_status
      debug:
        msg: "{{ is_remove }}"

    - name: install redis
      include_role:
        name: redis
      when:  is_remove == 'false'

    - name: uninstall redis
      shell: docker stack rm redis
      when: "is_remove == 'true'"
      run_once: true  
