tcp:
  routers:
    keystone-router:
      rule: "HostSNI(`*`)"
      service: keystone-service
      entryPoints:
        - keystone
    memcached-router:
      rule: "HostSNI(`*`)"
      service: memcached-service
      entryPoints:
        - memcached
    glance-api-router:
      rule: "HostSNI(`*`)"
      service: glance-api-service
      entryPoints:
        - glance-api
    placement-router:
      rule: "HostSNI(`*`)"
      service: placement-service
      entryPoints:
        - placement
    nova-api-router:
      rule: "HostSNI(`*`)"
      service: nova-api-service
      entryPoints:
        - nova-api
    nova-metadata-api-router:
      rule: "HostSNI(`*`)"
      service: nova-metadata-api-service
      entryPoints:
        - nova-metadata-api
    nova-novncproxy-router:
      rule: "HostSNI(`*`)"
      service: nova-novncproxy-service
      entryPoints:
        - nova-novncproxy
    neutron-server-router:
      rule: "HostSNI(`*`)"
      service: neutron-server-service
      entryPoints:
        - neutron-server
    cinder-api-router:
      rule: "HostSNI(`*`)"
      service: cinder-api-service
      entryPoints:
        - cinder-api
    keystone-admin-router:
      rule: "HostSNI(`*`)"
      service: keystone-admin-service
      entryPoints:
        - keystone-admin
    ceph-api-router:
      rule: "HostSNI(`*`)"
      service: ceph-api-service
      entryPoints:
        - ceph-api

  services:
    keystone-service:
      loadBalancer:
        servers:
    {% for host in  groups.get('managers')  %}
      - address: "{{ hostvars[host]['ansible_host'] }}:5000"
    {% endfor %}

            
    memcached-service:
      loadBalancer:
        servers:
    {% for host in  groups.get('managers')  %}
      - address: "{{ hostvars[host]['ansible_host'] }}:11211"
    {% endfor %}

            
    glance-api-service:
      loadBalancer:
        servers:
    {% for host in  groups.get('managers')  %}
      - address: "{{ hostvars[host]['ansible_host'] }}:9292"
    {% endfor %}

            
    placement-service:
      loadBalancer:
        servers:
    {% for host in  groups.get('managers')  %}
      - address: "{{ hostvars[host]['ansible_host'] }}:8780"
    {% endfor %}

            
    nova-api-service:
      loadBalancer:
        servers:
    {% for host in  groups.get('managers')  %}
      - address: "{{ hostvars[host]['ansible_host'] }}:8774"
    {% endfor %}

            
    nova-metadata-api-service:
      loadBalancer:
        servers:
    {% for host in  groups.get('managers')  %}
      - address: "{{ hostvars[host]['ansible_host'] }}:8775"
    {% endfor %}

            
    nova-novncproxy-service:
      loadBalancer:
        servers:
    {% for host in  groups.get('managers')  %}
      - address: "{{ hostvars[host]['ansible_host'] }}:6080"
    {% endfor %}
            
    neutron-server-service:
      loadBalancer:
        servers:
    {% for host in  groups.get('managers')  %}
      - address: "{{ hostvars[host]['ansible_host'] }}:9696"
    {% endfor %}

            
    cinder-api-service:
      loadBalancer:
        servers:
    {% for host in  groups.get('managers')  %}
      - address: "{{ hostvars[host]['ansible_host'] }}:8776"
    {% endfor %}

                   
    keystone-admin-service:
      loadBalancer:
        servers:
    {% for host in  groups.get('managers')  %}
      - address: "{{ hostvars[host]['ansible_host'] }}:35357"
    {% endfor %}

    ceph-api-service:
      loadBalancer:
        servers:
    {% for host in  groups.get('managers')  %}
      - address: "{{ hostvars[host]['ansible_host'] }}:8443"
    {% endfor %}

            
          
          
          
