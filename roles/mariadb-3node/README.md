# Database Configuration Script

This document describes how to use the `replace_db.sh` script to switch between MySQL and Dqlite database configurations.

## Usage

```bash
./script/replace_db.sh [mysql|dqlite]
```

## Examples

### Switch to MySQL
```bash
./script/replace_db.sh mysql
```

This will:
1. Set MySQL as the database driver
2. Configure MySQL credentials in config files
3. Update docker-compose.yml with MySQL environment variables
4. Remove any Dqlite-specific configurations

### Switch to Dqlite
```bash
./script/replace_db.sh dqlite
```

This will:
1. Set Dqlite as the database driver
2. Configure Dqlite settings in config files
3. Update docker-compose.yml with Dqlite environment variables
4. Remove any MySQL-specific configurations

## Important Notes
- The script modifies multiple configuration files:
  - conf/theauth/config.ini
  - conf/hci/settings.py
  - docker-compose.yml
- Always make sure to backup your configuration files before running the script
- The script requires root privileges to modify system files
- After running the script, you may need to restart your services for changes to take effect

## Environment Variables
- MySQL: `MYSQL_ROOT_PASSWORD: "thecloud2015.1"`
- Dqlite: `DQLITE_CLUSTER_ADDRESS: "db:9000"`


#创建config
```bash
docker config create mariadb_galera_cnf galera.cnf
```

#发布
```bash
docker deploy -c docker-compose.yml cl
```

#加上label
```bash
docker node update --label-add mariadb-node=1 controller1
docker node update --label-add mariadb-node=2 controller2
docker node update --label-add mariadb-node=3 controller3
```


# 更新服务移除 bootstrap 参数 这个参数只是第一个节点需要 之后的节点不需要 如果能够保证第一个节点不会被删除 可以不用更新
```bash
docker service update --args "mysqld" hci_mariadb
```

#查看node参数
```bash
[root@controller1 cl]# docker node ls -q | xargs -I {} docker node inspect {} --format '{{ .Description.Hostname }}: {{ .Spec.Labels }}'
controller1: map[mariadb-node:1]
controller2: map[mariadb-node:2]
controller3: map[mariadb-node:3]
```

# 查看所有节点及其标签
```bash
docker node ls -q | xargs docker node inspect -f '{{.ID}} [{{.Description.Hostname}}]: {{.Spec.Labels}}'
```

# 或者直接查看标签
```bash
docker node ls --format "{{.ID}}: {{.Labels}}"
```

# test 
```sql
CREATE TABLE user (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL
);
```


# 节点恢复
```bash
docker stack rm hci

# 删除 Galera 状态文件
rm -f /var/lib/docker/volumes/hci_db_data_1/_data/gvwstate.dat
rm -f /var/lib/docker/volumes/hci_db_data_1/_data/grastate.dat

# 对其他节点重复相同操作
rm -f /var/lib/docker/volumes/hci_db_data_2/_data/gv*state.dat
rm -f /var/lib/docker/volumes/hci_db_data_3/_data/gv*state.dat
```

