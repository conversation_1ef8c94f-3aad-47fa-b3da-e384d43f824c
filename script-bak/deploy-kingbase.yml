---
- name: deploy kingbase
  hosts: managers
  vars:
    src_docker_compose: docker-compose.yml
    dest_docker_compose: /etc/thestack/compose/docker-compose-kingbase.yml
    stack_name: kingbase
    is_remove: "{{ remove }}"
  tasks:

    - name: Print install_status
      debug:
        msg: "{{ is_remove }}"

    - name: install kingbase
      include_role:
        name: kingbase
      when:  is_remove == 'false'

    - name: uninstall kingbase
      shell: docker stack rm kingbase
      when: "is_remove == 'true'"
      run_once: true  
