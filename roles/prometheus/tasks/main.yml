---
- name: Create multiple directories
  file:
    path: "/etc/thestack/conf/{{ item }}"
    state: directory 
    mode: '0755'
    recurse: yes
  with_items:
    - prometheus
    - alertmanager

- name: Create multiple directories
  file:
    path: "/etc/thestack/conf/{{ item }}"
    state: directory 
    mode: '0755'
    recurse: yes
  with_items:
    - prometheus
    - alertmanager

- name: Create rule directories
  file:
    path: "/etc/thestack/conf/prometheus/rules"
    state: directory 
    mode: '0755'
    recurse: yes


- name: Upload configuration files from templates
  copy:
    src: "conf/{{ item.dir }}/{{ item.file }}"
    dest: "/etc/thestack/conf/{{ item.dir }}/{{ item.file }}"
    force: yes
    mode: '0644'
    backup: yes
  loop:
    - { dir: 'alertmanager', file: 'default.tmpl' }
    - { dir: 'alertmanager', file: 'alertmanager.yml' }

- name: Upload prometheus rule files
  copy:
    src: "conf/prometheus/rules/rules.yml"
    dest: "/etc/thestack/conf/prometheus/rules/rules.yml"
    force: yes
    mode: '0644'
    backup: yes


- name: Upload prometheus config files
  template:
    src: "prometheus.yml"
    dest: "/etc/thestack/conf/prometheus/prometheus.yml"
    force: yes
    mode: '0644'
    backup: yes

- name: Upload docker compose file
  template:
    src: "{{ src_docker_compose }}"
    dest: "{{ dest_docker_compose }}"
    force: yes
    mode: '0644'
    backup: yes
    
- name: Deploy services
  shell: docker stack deploy -c {{ dest_docker_compose }} {{ stack_name }}
  run_once: true

- name: Pause for 2 seconds to wait for services being running
  pause:
    seconds: 2
  run_once: true
  
- name: Ensure services deployed
  shell: docker service ls
  register: service_output

- name: Output service list
  debug:
    msg: "{{ service_output.stdout_lines }}"

- name: Show stacks
  shell: docker stack list
