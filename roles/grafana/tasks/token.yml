# - name: Execute curl command
#   shell: "curl --no-sessionid -s -X POST -H 'Content-Type: application/json' -d '{\"name\":\"apikeycurl\", \"role\": \"Admin\"}' http://admin:admin@{{ vip }}:3000/api/auth/keys"
#   register: curl_result
#   run_once: true

- name: Send POST request using uri module
  uri:
    url: "https://{{ vip }}:3000/api/auth/keys"
    method: POST
    body_format: json
    body:
      id: 1
      name: apikeycurl
      role: Admin
    headers:
      Content-Type: application/json
    validate_certs: no  
    status_code: 200     
    return_content: yes
    force_basic_auth: yes
    url_username: admin
    url_password: admin
    client_cert: "/etc/thestack/conf/traefik/yourdomain.crt"
    client_key: "/etc/thestack/conf/traefik/yourdomain.key"
  run_once: true
  register: uri_result
  until: uri_result.status == 200 or uri_result.status == 409
  retries: 30
  delay: 30
  failed_when: uri_result.status != 200 and uri_result.status != 409

- name: Save output to variable
  set_fact:
    api_key: "{{ uri_result.json }}"
  run_once: true

- name: Print install_status
  debug:
    msg: "{{ uri_result.status }}"

# - name: Create datasource
#   shell: "curl -X POST --insecure -H 'Authorization: Bearer {{ api_key }}' -H 'Content-Type: application/json' -H 'Accept: application/json' -d '{\"name\":\"Prometheus\",\"type\":\"Prometheus\",\"url\":\"http://{{ vip }}:9090\",\"access\":\"proxy\",\"basicAuth\":false,\"withCredentials\": false,\"isDefault\": true,\"jsonData\": {\"httpMethod\": \"POST\"},\"version\": 1,\"readOnly\": false}' http://{{ vip }}:3000/api/datasources"

- name: Create datasource
  shell: "curl --no-sessionid --location --request  --cert /etc/thestack/conf/traefik/yourdomain.crt --key /etc/thestack/conf/traefik/yourdomain.key -k -X POST 'https://{{ vip }}:3000/api/datasources' \
         --header 'Accept: application/json' \
         --header 'Authorization: Bearer {{ api_key.key }}' \
         --header 'Content-Type: application/json' \
         --data-raw '{
           \"name\": \"Prometheus\",
           \"type\": \"prometheus\",
           \"access\": \"proxy\",
           \"url\": \"http://{{ vip }}:9090\",
           \"basicAuth\": false,
           \"withCredentials\": false,
           \"isDefault\": true,
           \"jsonData\": {
             \"httpMethod\": \"POST\"
           },
           \"version\": 1,
           \"readOnly\": false
         }'"
  run_once: true
  when: uri_result.status == 200

- name: Create node_dashboard
  shell: "curl -X POST --no-sessionid --insecure --cert /etc/thestack/conf/traefik/yourdomain.crt --key /etc/thestack/conf/traefik/yourdomain.key -k -H 'Authorization: Bearer {{ api_key.key }}' -H 'Content-Type: application/json' -d @/etc/thestack/conf/grafana/node_dashboard.json https://{{ vip }}:3000/api/dashboards/db"
  run_once: true
  when: uri_result.status == 200

- name: Create windows_dashboard
  shell: "curl -X POST --no-sessionid --insecure --cert /etc/thestack/conf/traefik/yourdomain.crt --key /etc/thestack/conf/traefik/yourdomain.key -k -H 'Authorization: Bearer {{ api_key.key }}' -H 'Content-Type: application/json' -d @/etc/thestack/conf/grafana/windows_dashboard.json https://{{ vip }}:3000/api/dashboards/db"
  run_once: true
  when: uri_result.status == 200

- name: Create ceph_pools_dashboard
  shell: "curl -X POST --no-sessionid --insecure --cert /etc/thestack/conf/traefik/yourdomain.crt --key /etc/thestack/conf/traefik/yourdomain.key -k -H 'Authorization: Bearer {{ api_key.key }}' -H 'Content-Type: application/json' -d @/etc/thestack/conf/grafana/ceph_pools_dashboard.json https://{{ vip }}:3000/api/dashboards/db"
  run_once: true
  when: uri_result.status == 200

- name: Create ceph_cluster_dashboard
  shell: "curl -X POST --no-sessionid --insecure --cert /etc/thestack/conf/traefik/yourdomain.crt --key /etc/thestack/conf/traefik/yourdomain.key -k -H 'Authorization: Bearer {{ api_key.key }}' -H 'Content-Type: application/json' -d @/etc/thestack/conf/grafana/ceph_cluster_dashboard.json https://{{ vip }}:3000/api/dashboards/db"
  run_once: true
  when: uri_result.status == 200