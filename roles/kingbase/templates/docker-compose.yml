version: "3.7"

services:
  db:
    image: tianwen1:5000/kingbase_v008r006c008b0020_single_x86:v1
    # container_name: kingbase
    hostname: db
    # privileged: true
    ports:
      - "4321:54321"
    networks:
      - traefik_prod
    volumes:
      - thekingbase:/home/<USER>/userdata
    # configs:
    #   - source: mariadb_init.sql
    #     target: /docker-entrypoint-initdb.d/init.sql
       
    deploy:
      mode: replicated
      replicas: 1
      restart_policy:
        condition: any
        delay: 10s
        max_attempts: 3
        window: 180s
      placement:
        constraints:
          - node.role==manager
      labels:
        - traefik.enable=true
        - traefik.docker.network=traefik_prod
        - traefik.tcp.routers.kingbase.rule=HostSNI(`*`)
        - traefik.tcp.routers.kingbase.entrypoints=kingbase
        - traefik.tcp.routers.kingbase.tls=false
        - traefik.tcp.routers.kingbase.service=kingbase
        - traefik.tcp.services.kingbase.loadbalancer.server.port=4321

      update_config:
        parallelism: 1
        delay: 3m
    environment:
      - NEED_START=yes
      - DB_USER=root
      - DB_PASSWORD=thecloud2015.1
      - DB_MODE=oracle
    command: [/usr/sbin/init]

# configs:
#   mariadb_init.sql:
#     file: /etc/thestack/conf/mariadb/init.sql

networks:
  traefik_prod:
    external: true

volumes:
  thekingbase:
    external: true
