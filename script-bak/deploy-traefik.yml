---
- name: deploy traefik
  hosts: managers
  vars:
    src_docker_compose: docker-compose.yml
    dest_docker_compose: /etc/thestack/compose/docker-compose-traefik.yml
    stack_name: traefik
    is_remove: "{{ remove }}"
  tasks:

    - name: Print install_status
      debug:
        msg: "{{ is_remove }}"

    - name: install traefik
      include_role:
        name: traefik
      when:  is_remove == 'false'

    - name: uninstall traefik
      shell: docker stack rm traefik
      when: "is_remove == 'true'"
      run_once: true  
