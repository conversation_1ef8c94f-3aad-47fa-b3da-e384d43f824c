#!/bin/bash

set -x
# 设置 IP 地址、子网掩码、网关和网卡名称
CURRENT_IP=$1
IP_ADDRESS=$2
NETMASK=$3
GATEWAY=$4

echo "ip $IP_ADDRESS"
#TODO 通过SOURCE_IP 找到网卡设备名DEVICE
DEVICE=$(ip -o -4 addr show | grep -w "$CURRENT_IP" | awk '{print $2}')
#找不到匹配的网卡，将DEVICE设置为eth0
[ -z "$DEVICE" ] && DEVICE="eth0"
#输出找到的网卡信息
echo "IP地址: $CURRENT_IP 对应的网卡是: $DEVICE "
if [ -z "$DEVICE" ]; then
	exit 1
fi


# 获取当前 IP 地址、子网掩码和网关 改一下
#CURRENT_IP=$(ip addr show dev $DEVICE | grep "inet " | awk '{print $2}' | cut -d '/' -f 1)


CURRENT_NETMASK=$(ip addr show dev $DEVICE | grep "inet " | awk '{print $2}' | cut -d '/' -f 2)
CURRENT_GATEWAY=$(ip route show dev $DEVICE | grep "default via" | awk '{print $3}')
echo "current netmask $CURRENT_NETMASK"
# 如果当前 IP 地址、子网掩码和网关与新的 IP 地址、子网掩码和网关不同，就修改它们
if [[ "$CURRENT_IP" != "$IP_ADDRESS" ]] || [[ "$CURRENT_NETMASK" != "$NETMASK" ]] || [[ "$CURRENT_GATEWAY" != "$GATEWAY" ]]; then
    # 修改 IP 地址、子网掩码和网关
    echo "modfiy ip $IP_ADDRESS"
    ip addr add "$IP_ADDRESS/$NETMASK" dev $DEVICE
    ip addr del "$CURRENT_IP/$CURRENT_NETMASK" dev $DEVICE
    
    echo "add default route"
    ip route add default via "$GATEWAY" dev $DEVICE
    ip route del default via "$CURRENT_GATEWAY" dev $DEVICE

    # 重启网络服务
    #systemctl restart networking.service
	ifdown $DEVICE && ifup $DEVICE
	echo "restart device"

    echo "IP 地址、子网掩码和网关已修改为：$IP_ADDRESS、$NETMASK 和 $GATEWAY"
else
    echo "IP 地址、子网掩码和网关已经是：$IP_ADDRESS、$NETMASK 和 $GATEWAY"
fi

########################################
# 修改IPADDR
####################################

# 生成 ifcfg-eth0 文件
cat <<EOF > /etc/sysconfig/network-scripts/ifcfg-$DEVICE
TYPE=Ethernet
DEVICE=$DEVICE
BOOTPROTO=static
IPADDR=$IP_ADDRESS
PREFIX=$NETMASK
GATEWAY=$GATEWAY
ONBOOT=yes
EOF

echo "ifcfg-$DEVICE 文件已生成。"#!/bin/bash



