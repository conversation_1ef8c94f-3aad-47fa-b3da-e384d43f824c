version: '3.8'

x-mariadb-common: &mariadb-common
  image: tianwen1:5000/mariadb:10.6
  environment:
    - MARIADB_ROOT_PASSWORD=thecloud2015.1
    - MARIADB_DATABASE=hci_db
    - MARIADB_USER=hci
    - MARIADB_PASSWORD=thecloud2015.1
  networks:
    - prod


services:
  mariadb-1:
    <<: *mariadb-common
    configs:
      - source: mariadb_galera_cnf-1
        target: /etc/mysql/conf.d/galera.cnf
    deploy:
      mode: replicated
      replicas: 1
      placement:
        constraints:
          - node.labels.mariadb-node == 1
      restart_policy:
        condition: any
        delay: 5s
      endpoint_mode: vip
      labels:
        - "traefik.enable=true"
        - "traefik.tcp.routers.mariadb1.rule=HostSNI(`*`)"
        - "traefik.tcp.routers.mariadb1.entrypoints=mariadb"
        - "traefik.tcp.routers.mariadb1.service=mariadb-service"
        - "traefik.tcp.services.mariadb-service.loadbalancer.server.port=3306"
        - "traefik.tcp.services.mariadb-service.loadbalancer.sticky=true" 
    command: --wsrep-new-cluster
    volumes:
      - db_data_1:/var/lib/mysql
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-pthecloud2015.1"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  mariadb-2:
    <<: *mariadb-common
    configs:
      - source: mariadb_galera_cnf-2
        target: /etc/mysql/conf.d/galera.cnf
    deploy:
      mode: replicated
      replicas: 1
      placement:
        constraints:
          - node.labels.mariadb-node == 2
      restart_policy:
        condition: any
        delay: 5s
      endpoint_mode: vip
      labels:
        - "traefik.enable=true"
        - "traefik.tcp.routers.mariadb2.rule=HostSNI(`*`)"
        - "traefik.tcp.routers.mariadb2.entrypoints=mariadb"
        - "traefik.tcp.routers.mariadb2.service=mariadb-service"
        - "traefik.tcp.services.mariadb-service.loadbalancer.server.port=3306"
        - "traefik.tcp.services.mariadb-service.loadbalancer.sticky=true" 
    volumes:
      - db_data_2:/var/lib/mysql
    depends_on:
      - mariadb-1
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-pthecloud2015.1"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  mariadb-3:
    <<: *mariadb-common
    configs:
      - source: mariadb_galera_cnf-3
        target: /etc/mysql/conf.d/galera.cnf
    deploy:
      mode: replicated
      replicas: 1
      placement:
        constraints:
          - node.labels.mariadb-node == 3
      restart_policy:
        condition: any
        delay: 5s
      endpoint_mode: vip
      labels:
        - "traefik.enable=true"
        - "traefik.tcp.routers.mariadb3.rule=HostSNI(`*`)"
        - "traefik.tcp.routers.mariadb3.entrypoints=mariadb"
        - "traefik.tcp.routers.mariadb3.service=mariadb-service"
        - "traefik.tcp.services.mariadb-service.loadbalancer.server.port=3306"
        - "traefik.tcp.services.mariadb-service.loadbalancer.sticky=true" 
    volumes:
      - db_data_3:/var/lib/mysql
    depends_on:
      - mariadb-2
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-pthecloud2015.1"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

configs:
  mariadb_galera_cnf-1:
    file: /etc/thestack/conf/mariadb/galera.cnf-1
  mariadb_galera_cnf-2:
    file: /etc/thestack/conf/mariadb/galera.cnf-2
  mariadb_galera_cnf-3:
    file: /etc/thestack/conf/mariadb/galera.cnf-3
volumes:
  db_data_1:
  db_data_2:
  db_data_3:

networks:
  prod:
    name: prod
    external: true