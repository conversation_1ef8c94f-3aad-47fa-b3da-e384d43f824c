---
- name: deploy hci dashboard
  hosts: deploy
  vars:
    src_docker_compose: docker-compose.yml
    dest_docker_compose: /etc/thestack/compose/docker-compose-hci-dashboard.yml

    src_serialconfig:   conf/thewebvue/serialconfig.js
    dest_serialconfig:  /etc/thestack/conf/thewebvue/serialconfig.js

    src_thewebvue:      conf/thewebvue/config.js
    dest_thewebvue:    /etc/thestack/conf/thewebvue/config.js

    src_hci_web_conf: conf/hci/settings.py.j2
    dest_hci_web_conf: /etc/thestack/conf/hci/settings.py

    stack_name: hci-dashboard
    is_remove: "{{ remove }}"
  tasks:
    - name: install service
      shell: yum install  iscsi-initiator-utils  libvirt -y #ocfs2-utils ovs   合并代码 仓库 本地agent asyn-celery 在线部署跑通 iso打包（本地软件仓库）

    # - name: install ovs
    #   shell: yum install ./RPMS/* -y

    - name: Print install_status
      debug:
        msg: "{{ is_remove }}"

    - name: install dashboard
      include_role:
        name: "../roles/hci-dashboard"
      when:  is_remove == 'false'

    - name: uninstall dashboard
      shell: docker stack rm dashboard
      when: "is_remove == 'true'"
      run_once: true
