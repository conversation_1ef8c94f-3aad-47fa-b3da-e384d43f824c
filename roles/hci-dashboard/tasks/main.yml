---
- name: Create multiple directories
  file:
    path: "/etc/thestack/conf/{{ item }}"
    state: directory 
    mode: '0755'
    recurse: yes
  with_items:
    - hci
    - webvue
    - message
    - auth

- name: Upload configuration files from templates
  template:
    src: "conf/{{ item.dir }}/{{ item.file }}"
    dest: "/etc/thestack/conf/{{ item.dir }}/{{ item.file }}"
    force: yes
    mode: '0644'
    backup: yes
  loop:
    - { dir: 'auth', file: 'config.ini' }
    - { dir: 'hci', file: 'settings.py' }
    - { dir: 'webvue', file: 'serialconfig.js' }
    - { dir: 'webvue', file: 'config.js' }
    - { dir: 'message', file: 'message.yml' }

- name: Upload docker compose file
  template:
    src: "{{ src_docker_compose }}"
    dest: "{{ dest_docker_compose }}"
    force: yes
    mode: '0644'    # 可选：设置文件权限
    backup: yes     # 备份
    
- name: Deploy services
  shell: docker stack deploy -c {{ dest_docker_compose }} {{ stack_name }}
  run_once: true

- name: Pause for 10 seconds to wait for services being running
  pause:
    seconds: 10
  run_once: true
  
- name: Ensure services deployed
  shell: docker service ls
  register: service_output

- name: Output service list
  debug:
    msg: "{{ service_output.stdout_lines }}"

- name: Show stacks
  shell: docker stack list