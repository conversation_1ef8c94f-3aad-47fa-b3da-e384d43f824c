groups:
- name: testrule
  rules:

  - alert: 服务器离线
    expr: (up{job="node-exporter"}) == 0
    for: 0m
    labels:
      severity: critical
    annotations:
      summary: 服务器离线 (instance {{ $labels.instance }})
      description: 服务器离线.  VALUE = {{ $value }}  LABELS = {{ $labels }}

  - alert: 节点失联
    expr: up{job="openstacklinux"} == 0 or up{job="openstackwindows"} == 0
    for: 3m
    labels:
      severity: info
    annotations:
      summary: 节点失联 (instance {{ $labels.instance }})
      description: 节点失联.\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}

  - alert: 任务失联
    expr: absent(up{job="prometheus"})
    for: 3m
    labels:
      severity: warning
    annotations:
      summary: 任务失联 (instance {{ $labels.instance }})
      description: 任务失联\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}



  - alert: 节点CPU负载过高
    expr: 100 - (avg by(instance) (rate(node_cpu_seconds_total{mode="idle"}[2m])) * 100) > 90
    for: 0m
    labels:
      severity: critical
    annotations:
      summary: 节点CPU负载过高 (instance {{ $labels.instance }})
      description: 节点CPU负载过高 VALUE = {{ $value }}\n  LABELS = {{ $labels }}

  - alert: 节点CPU负载过高
    expr: 100 - (avg by(instance) (rate(node_cpu_seconds_total{mode="idle"}[2m])) * 100) > 80
    for: 0m
    labels:
      severity: major
    annotations:
      summary: 节点CPU负载过高 (instance {{ $labels.instance }})
      description: 节点CPU负载过高 VALUE = {{ $value }}\n  LABELS = {{ $labels }}

  - alert: 节点CPU负载过高
    expr: 100 - (avg by(instance) (rate(node_cpu_seconds_total{mode="idle"}[2m])) * 100) > 70
    for: 0m
    labels:
      severity: warning
    annotations:
      summary: 节点CPU负载过高 (instance {{ $labels.instance }})
      description: 节点CPU负载过高 VALUE = {{ $value }}\n  LABELS = {{ $labels }}

  - alert: 节点CPU负载过高
    expr: 100 - (avg by(instance) (rate(node_cpu_seconds_total{mode="idle"}[2m])) * 100) > 60
    for: 0m
    labels:
      severity: info
    annotations:
      summary: 节点CPU负载过高 (instance {{ $labels.instance }})
      description: 节点CPU负载过高 VALUE = {{ $value }}\n  LABELS = {{ $labels }}





  - alert: 节点内存占用过高
    expr: 100 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes * 100) > 90
    for: 2m
    labels:
      severity: critical
    annotations:
      summary: 节点内存占用过高 (instance {{ $labels.instance }})
      description: 节点内存占用过高 VALUE = {{ $value }}\n  LABELS = {{ $labels }}

  - alert: 节点内存占用过高
    expr: 100 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes * 100) > 80
    for: 2m
    labels:
      severity: major
    annotations:
      summary: 节点内存占用过高 (instance {{ $labels.instance }})
      description: 节点内存占用过高 VALUE = {{ $value }}\n  LABELS = {{ $labels }}

  - alert: 节点内存占用过高
    expr: 100 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes * 100) > 70
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: 节点内存占用过高 (instance {{ $labels.instance }})
      description: 节点内存占用过高 VALUE = {{ $value }}\n  LABELS = {{ $labels }}

  - alert: 节点内存占用过高
    expr: 100 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes * 100) > 60
    for: 2m
    labels:
      severity: info
    annotations:
      summary: 节点内存占用过高 (instance {{ $labels.instance }})
      description: 节点内存占用过高 VALUE = {{ $value }}\n  LABELS = {{ $labels }}




  - alert: 磁盘空间不足
    expr: 100 - (node_filesystem_files_free{mountpoint ="/rootfs"} / node_filesystem_files{mountpoint="/rootfs"} * 100) > 90 and ON (instance, device, mountpoint) node_filesystem_readonly{mountpoint="/rootfs"} == 90
    for: 2m
    labels:
      severity: critical
    annotations:
      summary: 磁盘空间不足 (instance {{ $labels.instance }})
      description: 磁盘空间不足 VALUE = {{ $value }}\n  LABELS = {{ $labels }}

  - alert: 磁盘空间不足
    expr: 100 - (node_filesystem_files_free{mountpoint ="/rootfs"} / node_filesystem_files{mountpoint="/rootfs"} * 100) > 80 and ON (instance, device, mountpoint) node_filesystem_readonly{mountpoint="/rootfs"} == 90
    for: 2m
    labels:
      severity: major
    annotations:
      summary: 磁盘空间不足 (instance {{ $labels.instance }})
      description: 磁盘空间不足 VALUE = {{ $value }}\n  LABELS = {{ $labels }}

  - alert: 磁盘空间不足
    expr: 100 - (node_filesystem_files_free{mountpoint ="/rootfs"} / node_filesystem_files{mountpoint="/rootfs"} * 100) > 70 and ON (instance, device, mountpoint) node_filesystem_readonly{mountpoint="/rootfs"} == 90
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: 磁盘空间不足 (instance {{ $labels.instance }})
      description: 磁盘空间不足 VALUE = {{ $value }}\n  LABELS = {{ $labels }}

  - alert: 磁盘空间不足
    expr: 100 - (node_filesystem_files_free{mountpoint ="/rootfs"} / node_filesystem_files{mountpoint="/rootfs"} * 100) > 60 and ON (instance, device, mountpoint) node_filesystem_readonly{mountpoint="/rootfs"} == 90
    for: 2m
    labels:
      severity: info
    annotations:
      summary: 磁盘空间不足 (instance {{ $labels.instance }})
      description: 磁盘空间不足 VALUE = {{ $value }}\n  LABELS = {{ $labels }}



 
  - alert: 网络带库占用过高
    expr: sum by (instance) (rate(node_network_transmit_bytes_total[2m])) / 1024 / 1024 > 100
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: 网络带库占用过高 (instance {{ $labels.instance }})
      description: 网络带库占用过高 VALUE = {{ $value }}\n  LABELS = {{ $labels }}
 
  - alert: 网络带库占用过高
    expr: sum by (instance) (rate(node_network_transmit_bytes_total[2m])) / 1024 / 1024 > 90
    for: 5m
    labels:
      severity: major
    annotations:
      summary: 网络带库占用过高 (instance {{ $labels.instance }})
      description: 网络带库占用过高 VALUE = {{ $value }}\n  LABELS = {{ $labels }}
 
  - alert: 网络带库占用过高
    expr: sum by (instance) (rate(node_network_transmit_bytes_total[2m])) / 1024 / 1024 > 80
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: 网络带库占用过高 (instance {{ $labels.instance }})
      description: 网络带库占用过高  VALUE = {{ $value }}\n  LABELS = {{ $labels }}
  - alert: 网络带库占用过高
    expr: sum by (instance) (rate(node_network_transmit_bytes_total[2m])) / 1024 / 1024 > 70
    for: 5m
    labels:
      severity: info
    annotations:
      summary: 网络带库占用过高 (instance {{ $labels.instance }})
      description: 网络带库占用过高 VALUE = {{ $value }}\n  LABELS = {{ $labels }}





  - alert: 磁盘使用率过高
    expr: sum by (instance) (rate(node_disk_read_bytes_total[2m])) / 1024 / 1024 > 80
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: 磁盘使用率过高 (instance {{ $labels.instance }})
      description: 磁盘使用率过高 VALUE = {{ $value }}\n  LABELS = {{ $labels }}

  - alert: 磁盘使用率过高
    expr: sum by (instance) (rate(node_disk_read_bytes_total[2m])) / 1024 / 1024 > 70
    for: 5m
    labels:
      severity: major
    annotations:
      summary: 磁盘使用率过高 (instance {{ $labels.instance }})
      description: 磁盘使用率过高  VALUE = {{ $value }}\n  LABELS = {{ $labels }}

  - alert: 磁盘使用率过高
    expr: sum by (instance) (rate(node_disk_read_bytes_total[2m])) / 1024 / 1024 > 60
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: 磁盘使用率过高 (instance {{ $labels.instance }})
      description: 磁盘使用率过高  VALUE = {{ $value }}\n  LABELS = {{ $labels }}

  - alert: 磁盘使用率过高
    expr: sum by (instance) (rate(node_disk_read_bytes_total[2m])) / 1024 / 1024 > 50
    for: 5m
    labels:
      severity: info
    annotations:
      summary: 磁盘使用率过高 (instance {{ $labels.instance }})
      description: 磁盘使用率过高  VALUE = {{ $value }}\n  LABELS = {{ $labels }}




  - alert: 磁盘可用空间不足
    expr: 100 - ((node_filesystem_avail_bytes * 100) / node_filesystem_size_bytes) > 90 and ON (instance, device, mountpoint) node_filesystem_readonly == 0
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: 磁盘可用空间不足 (instance {{ $labels.instance }})
      description: 磁盘可用空间不足  VALUE = {{ $value }}\n  LABELS = {{ $labels }}

  - alert: 磁盘可用空间不足
    expr: 100 - ((node_filesystem_avail_bytes * 100) / node_filesystem_size_bytes) > 80 and ON (instance, device, mountpoint) node_filesystem_readonly == 0
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: 磁盘可用空间不足 (instance {{ $labels.instance }})
      description: 磁盘可用空间不足  VALUE = {{ $value }}\n  LABELS = {{ $labels }}

  - alert: 磁盘可用空间不足
    expr: 100 - ((node_filesystem_avail_bytes * 100) / node_filesystem_size_bytes) > 70 and ON (instance, device, mountpoint) node_filesystem_readonly == 0
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: 磁盘可用空间不足 (instance {{ $labels.instance }})
      description: 磁盘可用空间不足  VALUE = {{ $value }}\n  LABELS = {{ $labels }}

  - alert: 磁盘可用空间不足
    expr: 100 - ((node_filesystem_avail_bytes * 100) / node_filesystem_size_bytes) > 60 and ON (instance, device, mountpoint) node_filesystem_readonly == 0
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: 磁盘可用空间不足 (instance {{ $labels.instance }})
      description: 磁盘可用空间不足  VALUE = {{ $value }}\n  LABELS = {{ $labels }}




- name: dashboard
  rules:
  - alert: 分布式存储健康状态告警
    expr: ceph_health_status == 1
    for: 1m
    labels:
      severity: warning
      job: ceph
    annotations:
      summary: 分布式存储健康状态告警
      description: 分布式存储健康状态告警

  - alert: 分布式存储健康状态异常
    expr: ceph_health_status > 1
    for: 1m
    labels:
      severity: critical
      job: ceph
    annotations:
      summary: 分布式存储健康状态异常
      description: 分布式存储健康状态异常



  - alert: 存储单元可用空间不足
    expr: (ceph_osd_stat_bytes_used / ceph_osd_stat_bytes) * 100 > 90
    for: 1m
    labels:
      severity: critical
      job: ceph
    annotations:
      summary: 存储单元可用空间不足
      description: 存储单元可用空间不足 VALUE = {{ $value }}\n  LABELS = {{ $labels }}

  - alert: 存储单元可用空间不足
    expr: (ceph_osd_stat_bytes_used / ceph_osd_stat_bytes) * 100 > 80
    for: 1m
    labels:
      severity: major
      job: ceph
    annotations:
      summary: 存储单元可用空间不足
      description: 存储单元可用空间不足 VALUE = {{ $value }}\n  LABELS = {{ $labels }}

  - alert: 存储单元可用空间不足
    expr: (ceph_osd_stat_bytes_used / ceph_osd_stat_bytes) * 100 > 70
    for: 1m
    labels:
      severity: warning
      job: ceph
    annotations:
      summary: 存储单元可用空间不足
      description: 存储单元可用空间不足 VALUE = {{ $value }}\n  LABELS = {{ $labels }}

  - alert: 存储单元可用空间不足
    expr: (ceph_osd_stat_bytes_used / ceph_osd_stat_bytes) * 100 > 60
    for: 1m
    labels:
      severity: info
      job: ceph
    annotations:
      summary: 存储单元可用空间不足
      description: 存储单元可用空间不足 VALUE = {{ $value }}\n  LABELS = {{ $labels }}




  - alert: 对象存储守护进程已停止运行
    expr: (ceph_osd_up) * 100 < 50
    for: 1m
    labels:
      severity: warning
      job: ceph
    annotations:
      summary: 对象存储守护进程已停止运行
      description: 对象存储守护进程已停止运行 VALUE = {{ $value }}\n  LABELS = {{ $labels }}

  - alert: 对象存储守护进程所在主机已停止运行
    expr: count by(instance) (ceph_disk_occupation * on(ceph_daemon) group_right(instance) ceph_osd_up == 0) - count by(instance) (ceph_disk_occupation) == 0
    for: 1m
    labels:
      severity: warning
      job: ceph
    annotations:
      summary: 对象存储守护进程所在主机已停止运行
      description: 对象存储守护进程所在主机已停止运行 VALUE = {{ $value }}\n  LABELS = {{ $labels }}

  - alert: 分布式存储放置组阻塞
    expr: max(ceph_osd_numpg) > scalar(ceph_pg_active)
    for: 1m
    labels:
      severity: warning
      job: ceph
    annotations:
      summary: 分布式存储放置组阻塞
      description: 分布式存储放置组阻塞 VALUE = {{ $value }}\n  LABELS = {{ $labels }}

  - alert: 储单元响应缓慢
    expr: ((irate(node_disk_read_time_seconds_total[5m]) / clamp_min(irate(node_disk_reads_completed_total[5m]), 1) + irate(node_disk_write_time_seconds_total[5m]) / clamp_min(irate(node_disk_writes_completed_total[5m]), 1)) and on (instance, device) ceph_disk_occupation) > 1
    for: 1m
    labels:
      severity: warning
      job: ceph
    annotations:
      summary: 存储单元响应缓慢
      description: 存储单元响应缓慢 VALUE = {{ $value }}\n  LABELS = {{ $labels }}



  - alert: 网络错误
    expr: sum by (instance, device) (irate(node_network_receive_drop_total{device=~"(eth|en|bond|ib|mlx|p).*"}[5m]) + irate(node_network_receive_errs_total{device=~"(eth|en|bond|ib|mlx|p).*"}[5m]) + irate(node_network_transmit_drop_total{device=~"(eth|en|bond|ib|mlx|p).*"}[5m]) + irate(node_network_transmit_errs_total{device=~"(eth|en|bond|ib|mlx|p).*"}[5m])) > 30
    for: 1m
    labels:
      severity: cirtical
      job: ceph
    annotations:
      summary: 网络错误
      description: 网络错误，丢包率过高 VALUE = {{ $value }}\n  LABELS = {{ $labels }}

  - alert: 网络错误
    expr: sum by (instance, device) (irate(node_network_receive_drop_total{device=~"(eth|en|bond|ib|mlx|p).*"}[5m]) + irate(node_network_receive_errs_total{device=~"(eth|en|bond|ib|mlx|p).*"}[5m]) + irate(node_network_transmit_drop_total{device=~"(eth|en|bond|ib|mlx|p).*"}[5m]) + irate(node_network_transmit_errs_total{device=~"(eth|en|bond|ib|mlx|p).*"}[5m])) > 20
    for: 1m
    labels:
      severity: major
      job: ceph
    annotations:
      summary: 网络错误
      description: 网络错误，丢包率过高 VALUE = {{ $value }}\n  LABELS = {{ $labels }}

  - alert: 网络错误
    expr: sum by (instance, device) (irate(node_network_receive_drop_total{device=~"(eth|en|bond|ib|mlx|p).*"}[5m]) + irate(node_network_receive_errs_total{device=~"(eth|en|bond|ib|mlx|p).*"}[5m]) + irate(node_network_transmit_drop_total{device=~"(eth|en|bond|ib|mlx|p).*"}[5m]) + irate(node_network_transmit_errs_total{device=~"(eth|en|bond|ib|mlx|p).*"}[5m])) > 10
    for: 1m
    labels:
      severity: warning
      job: ceph
    annotations:
      summary: 网络错误
      description: 网络错误，丢包率过高 VALUE = {{ $value }}\n  LABELS = {{ $labels }}

  - alert: 网络错误
    expr: sum by (instance, device) (irate(node_network_receive_drop_total{device=~"(eth|en|bond|ib|mlx|p).*"}[5m]) + irate(node_network_receive_errs_total{device=~"(eth|en|bond|ib|mlx|p).*"}[5m]) + irate(node_network_transmit_drop_total{device=~"(eth|en|bond|ib|mlx|p).*"}[5m]) + irate(node_network_transmit_errs_total{device=~"(eth|en|bond|ib|mlx|p).*"}[5m])) > 5
    for: 1m
    labels:
      severity: info
      job: ceph
    annotations:
      summary: 网络错误
      description: 网络错误，丢包率过高 VALUE = {{ $value }}\n  LABELS = {{ $labels }}




  - alert: 存储池可用空间不足
    expr: (ceph_pool_stored / (ceph_pool_stored + ceph_pool_max_avail) * 100 + on (pool_id) group_left (name) (ceph_pool_metadata*0)) > 90
    for: 1m
    labels:
      severity: cirtical
      job: ceph
    annotations:
      summary: 存储池可用空间不足
      description: 存储池可用空间不足 VALUE = {{ $value }}\n  LABELS = {{ $labels }}

  - alert: 存储池可用空间不足
    expr: (ceph_pool_stored / (ceph_pool_stored + ceph_pool_max_avail) * 100 + on (pool_id) group_left (name) (ceph_pool_metadata*0)) > 80
    for: 1m
    labels:
      severity: major
      job: ceph
    annotations:
      summary: 存储池可用空间不足
      description: 存储池可用空间不足 VALUE = {{ $value }}\n  LABELS = {{ $labels }}

  - alert: 存储池可用空间不足
    expr: (ceph_pool_stored / (ceph_pool_stored + ceph_pool_max_avail) * 100 + on (pool_id) group_left (name) (ceph_pool_metadata*0)) > 70
    for: 1m
    labels:
      severity: warning
      job: ceph
    annotations:
      summary: 存储池可用空间不足
      description: 存储池可用空间不足 VALUE = {{ $value }}\n  LABELS = {{ $labels }}

  - alert: 存储池可用空间不足
    expr: (ceph_pool_stored / (ceph_pool_stored + ceph_pool_max_avail) * 100 + on (pool_id) group_left (name) (ceph_pool_metadata*0)) > 60
    for: 1m
    labels:
      severity: info
      job: ceph
    annotations:
      summary: 存储池可用空间不足
      description: 存储池可用空间不足 VALUE = {{ $value }}\n  LABELS = {{ $labels }}





  - alert: 分布式存储监控服务已停止运行
    expr: ceph_mon_quorum_status != 1
    for: 1m
    labels:
      severity: warning
      job: ceph
    annotations:
      summary: 分布式存储监控服务已停止运行
      description: 分布式存储监控服务已停止运行 VALUE = {{ $value }}\n  LABELS = {{ $labels }}



  - alert: 存储集群可用空间不足
    expr: (sum(ceph_osd_stat_bytes_used) / sum(ceph_osd_stat_bytes)) * 100 > 90
    for: 1m
    labels:
      severity: critical
      job: ceph
    annotations:
      summary: 存储集群可用空间过低
      description: 存储集群可用空间不足 VALUE = {{ $value }}\n  LABELS = {{ $labels }}

  - alert: 存储集群可用空间不足
    expr: (sum(ceph_osd_stat_bytes_used) / sum(ceph_osd_stat_bytes)) * 100 > 80
    for: 1m
    labels:
      severity: major
      job: ceph
    annotations:
      summary: 存储集群可用空间不足
      description: 存储集群可用空间不足 VALUE = {{ $value }}\n  LABELS = {{ $labels }}

  - alert: 存储集群可用空间不足
    expr: (sum(ceph_osd_stat_bytes_used) / sum(ceph_osd_stat_bytes)) * 100 > 70
    for: 1m
    labels:
      severity: warning
      job: ceph
    annotations:
      summary: 存储集群可用空间不足
      description: 存储集群可用空间不足 VALUE = {{ $value }}\n  LABELS = {{ $labels }}

  - alert: 存储集群可用空间不足
    expr: (sum(ceph_osd_stat_bytes_used) / sum(ceph_osd_stat_bytes)) * 100 > 60
    for: 1m
    labels:
      severity: info
      job: ceph
    annotations:
      summary: 存储集群可用空间不足
      description: 存储集群可用空间不足 VALUE = {{ $value }}\n  LABELS = {{ $labels }}



  - alert: 对象存储守护进程放置组数量过高
    expr: ceph_osd_numpg > 300
    for: 1m
    labels:
      severity: critical
      job: ceph
    annotations:
      summary: 对象存储守护进程放置组数量过高
      description: 对象存储守护进程放置组数量过高 VALUE = {{ $value }}\n  LABELS = {{ $labels }}

  - alert: 对象存储守护进程放置组数量过高
    expr: ceph_osd_numpg > 275
    for: 1m
    labels:
      severity: major
      job: ceph
    annotations:
      summary: 对象存储守护进程放置组数量过高
      description: 对象存储守护进程放置组数量过高 VALUE = {{ $value }}\n  LABELS = {{ $labels }}

  - alert: 对象存储守护进程放置组数量过高
    expr: ceph_osd_numpg > 250
    for: 1m
    labels:
      severity: warning
      job: ceph
    annotations:
      summary: 对象存储守护进程放置组数量过高
      description: 对象存储守护进程放置组数量过高 VALUE = {{ $value }}\n  LABELS = {{ $labels }}

  - alert: 对象存储守护进程放置组数量过高
    expr: ceph_osd_numpg > 225
    for: 1m
    labels:
      severity: info
      job: ceph
    annotations:
      summary: 对象存储守护进程放置组数量过高
      description: 对象存储守护进程放置组数量过高 VALUE = {{ $value }}\n  LABELS = {{ $labels }}
      