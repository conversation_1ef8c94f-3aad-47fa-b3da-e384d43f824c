[{"id": "theweb-api", "upstream": {"url": "http://theweb:8006", "strip_path": "the<PERSON>i"}, "match": {"url": "http://oathkeeper:4455/theapi/<.*>", "methods": ["GET", "POST", "PUT", "DELETE"]}, "authenticators": [{"handler": "cookie_session"}], "authorizer": {"handler": "allow"}, "mutators": [{"handler": "noop"}]}, {"id": "theweb-user", "upstream": {"url": "http://theuser:8089", "strip_path": "user"}, "match": {"url": "http://oathkeeper:4455/user/<.*>", "methods": ["GET", "POST", "PUT", "DELETE"]}, "authenticators": [{"handler": "cookie_session"}], "authorizer": {"handler": "allow"}, "mutators": [{"handler": "noop"}]}, {"id": "theweb-cloudy", "upstream": {"url": "http://thecloudy:8091", "strip_path": "cloudy"}, "match": {"url": "http://oathkeeper:4455/cloudy/<.*>", "methods": ["GET", "POST", "PUT", "DELETE"]}, "authenticators": [{"handler": "cookie_session"}], "authorizer": {"handler": "allow"}, "mutators": [{"handler": "noop"}]}, {"id": "theweb-log", "upstream": {"url": "http://thelog:8093", "strip_path": "log"}, "match": {"url": "http://oathkeeper:4455/log/<.*>", "methods": ["GET", "POST", "PUT", "DELETE"]}, "authenticators": [{"handler": "cookie_session"}], "authorizer": {"handler": "allow"}, "mutators": [{"handler": "noop"}]}, {"id": "thedesk", "upstream": {"url": "http://thedesk:8085", "strip_path": "desk"}, "match": {"url": "http://oathkeeper:4455/desk/<.*>", "methods": ["GET", "POST", "PUT", "DELETE"]}, "authenticators": [{"handler": "cookie_session"}], "authorizer": {"handler": "allow"}, "mutators": [{"handler": "noop"}]}]