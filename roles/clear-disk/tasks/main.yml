---
- name: Get root device
  shell: awk '$2=="/" {print $1}' /proc/mounts | head -n1
  register: root_device_result

- name: Set root device fact
  set_fact:
    root_device: "{{ root_device_result.stdout }}"

- name: Get real device path
  command: readlink -f "{{ root_device }}"
  register: real_device_result

- name: Set initial real device fact
  set_fact:
    real_device: "{{ real_device_result.stdout }}"

- name: Resolve physical disk for LVM or device mapper
  block:
    - name: Get slaves
      command: ls /sys/class/block/{{ real_device | basename }}/slaves
      register: slaves_result
      changed_when: false
      failed_when: false

    - name: Update real device if slaves exist
      set_fact:
        real_device: "/dev/{{ slaves_result.stdout_lines[0] }}"
      when: slaves_result.rc == 0 and slaves_result.stdout != ""
  when: "'/mapper/' in real_device or '/dm-' in real_device"

- name: Repeat resolution for nested mappings
  include_tasks: resolve_physical_disk.yml
  loop: "{{ range(0, 10)|list }}"
  loop_control:
    loop_var: outer_item

- name: Extract root disk name
  set_fact:
    root_disk: "{{ real_device | regex_replace('^/dev/([a-z]+)[0-9]*$', '\\1') }}"

- name: Display root disk
  debug:
    var: root_disk

- name: Get all disks
  command: lsblk -ndo NAME
  register: all_disks

- name: Remove LVM and dm devices for non-system disks
  shell: |
    if lvs | grep -q /dev/{{ item }}; then
      lvremove -f /dev/{{ item }}
    fi
    if vgs | grep -q /dev/{{ item }}; then
      vgremove -f /dev/{{ item }}
    fi
    if ls /dev/mapper/ceph-* | grep -q {{ item }}; then
      dmsetup remove_all
    fi
  ignore_errors: yes
  loop: "{{ all_disks.stdout_lines }}"
  when: "item != root_disk"

- name: Erase partition table and filesystem signatures for non-system disks
  command: wipefs -af /dev/{{ item }}
  ignore_errors: yes
  loop: "{{ all_disks.stdout_lines }}"
  when: "item != root_disk"

- name: Stop and remove RAID arrays for non-system disks if exists
  shell: |
    if mdadm --detail /dev/{{ item }} &> /dev/null; then
      mdadm --stop /dev/{{ item }}
      mdadm --remove /dev/{{ item }}
    fi
  ignore_errors: yes
  loop: "{{ all_disks.stdout_lines }}"
  when: "item != root_disk"

- name: Display completion message
  debug:
    msg: "Disk clearing operation completed."