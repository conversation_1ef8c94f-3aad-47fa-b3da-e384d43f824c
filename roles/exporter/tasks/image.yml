---
- name: Check images exists
  shell: podman images | grep node-exporter | wc -l
  register: imgs

- name: Download node-exporter images
  get_url:
    url: http://{{ images_download_address }}/images/{{ item }}.tar
    dest: /etc/thestack/{{ item }}.tar
  when: imgs.stdout == "0"
  loop:
    - node-exporter

- name: Load node-exporter images
  shell: podman load -q -i /etc/thestack/{{ item }}.tar
  when: imgs.stdout == "0"
  loop:
    - node-exporter
  
- name: Check images exists
  shell: podman images | grep disk-exporter | wc -l
  register: imgs

- name: Download disk-exporter images
  get_url:
    url: http://{{ images_download_address }}/images/{{ item }}.tar
    dest: /etc/thestack/{{ item }}.tar
  when: imgs.stdout == "0"
  loop:
    - disk-exporter

- name: Load disk-exporter images
  shell: podman load -q -i /etc/thestack/{{ item }}.tar
  when: imgs.stdout == "0"
  loop:
    - disk-exporter

- name: Load go-exporter images
  shell: podman load -q -i /etc/thestack/{{ item }}.tar
  when: imgs.stdout == "0"
  loop:
    - go-exporter
