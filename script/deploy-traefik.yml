---
- name: deploy traefik
  hosts: deploy
  vars:
    src_docker_compose: docker-compose.yml
    dest_docker_compose: /etc/thestack/compose/docker-compose-traefik.yml

    src_traefik_routter_conf: certs-traefik.yml
    dest_traefik_routter_conf: /etc/thestack/conf/traefik/certs-traefik.yml

    src_traefik_cert: yourdomain.crt
    dest_traefik_cert: /etc/thestack/conf/traefik/yourdomain.crt
    
    src_traefik_key: yourdomain.key
    dest_traefik_key: /etc/thestack/conf/traefik/yourdomain.key

    src_traefik_tcp: tcp_routers.yml
    dest_traefik_tcp: /etc/thestack/conf/traefik/tcp_routers.yml


    stack_name: traefik
    is_remove: "{{ remove }}"
  tasks:

    - name: Print install_status
      debug:
        msg: "{{ is_remove }}"

    - name: install traefik
      include_role:
        name: "../roles/traefik"
      when:  is_remove == 'false'

    - name: uninstall traefik
      shell: docker stack rm traefik
      when: "is_remove == 'true'"
      run_once: true  
