#AUTH_URI = "http://keystone.openstack.svc.cluster.local/v3"
AUTH_URI = "http://{{ vip }}:35358/v3"
AUTH_TYPE = "password"
PROJECT_DOMAIN_ID = "default"
USER_DOMAIN_ID = "default"
PROJECT_NAME = "admin"
USERNAME = "admin"
PASSWORD = "thecloud2015.1"

ZUN_AUTH_URI = "http://{{ vip }}:5000/v3"
ZUN_AUTH_TYPE = "password"
ZUN_PROJECT_DOMAIN_ID = "default"
ZUN_USER_DOMAIN_ID = "default"
ZUN_PROJECT_NAME = "admin"
ZUN_USERNAME = "admin"
ZUN_PASSWORD = "admin"

REDIS_HOSTNAME = "redis"
REDIS_PWD = ""
REDIS_PORT = "6379"

DB_USERNAME = "root"
DB_PASSWORD = "thecloud2015.1"
DB_HOSTNAME = "db"
DB_PORT = "3306"
DB_DATABASE = "the"

FLAVOR_UUID = "cc13cd69-f831-4157-ad02-88ce0123e195"
NETWORK_UUID = "b68f09d5-15af-4ffd-b1b1-1050c732bd79"

LOG_PATH = "/var/log/the/theweb.log"
LOG_UPLOAD_PATH = "/var/log/the/theupload.log"
LOG_USER_PATH = "/var/log/the/theuser.log"

GRAFANA_AUTH_URI="*******************************"

GRANFA_LOG_ADDR = "http://grafana:3000/api/datasources/proxy/2/_msearch"

RROMETHEUS_ALERT_URI = "http://prometheus:9090/api/v1/alerts"
RROMETHEUS_QUERY_URI = "http://prometheus:9090/api/v1/query"

#使用VIP开放grafana
VM_MON_URL = "http://{{ vip }}:3000/d/Kdh0OoSGz/1-windows_exporter-for-prometheus-dashboard-cn-v20201012?orgId=1&kiosk"
HOST_MON_URL = "http://{{ vip }}:3000/d/thecloud/1-node-exporter-for-prometheus-dashboard-cn-v20201010?orgId=1&kiosk"
CEPH_URL = "http://{{ vip }}:3000/d/r6lloPJmz/ceph-cluster?orgId=1&refresh=1m&kiosk"
POOL_URL = "http://{{ vip }}:3000/d/-gtf0Bzik/ceph-pools?orgId=1&refresh=1m&kiosk"

#SHARE_DIR = "/data/share/"

THESC_URI="http://recommend:9002"

MIGRATE_TYPE = "step" 
MIGRATE_TMP_PATH = "/data/migrate"
MIGRATE_TMP_STEP_PATH = "/data/migrate"
MIGRATE_SSH_HOST = "*************"
MIGRATE_SSH_USER = "root"
MIGRATE_SSH_PWD = "thecloud2015.1"

HOST_DATA_KEY = {
    "node_cpu_seconds_total",  
    "node_cpu_usage",  
    "node_cpu_count", 
    "node_memory_usage", 
    "node_memory_total",
    "vm_process_cpu_seconds",
    "vm_cpu_count",
    "vm_process_memory",
    "vm_memory_maximum",
    "vm_usable_memory",
    "vm_memory_usage",
    "vm_status"
    "cluster_host"
}


HOST_JOB = "node-exporter"
VM_JOB = "qemu"


RECYCLE_DAYS = 10
ESX_PW = { "***********" :  "Qa@SmX7!" }
AC_URI="http://ac:8181"

RE_PRESSION_PATH = {
    "/v1/id/clusters/\d+":["sysadm", "operator"],
    "/v1/flavors/delete/\d+":["sysadm", "operator"]
}

PRESSION_PATH = {
    "/v1/monitor":["sysadm", "operator"], 
    "/v1/clusters/delete":["sysadm"], 
    "/v1/all/clusters":["sysadm", "operator"], 
    "/v1/grant/instances":["sysadm", "secadm"], 
    "/v1/grant/instances/to/user":["sysadm", "secadm"], 
    "/v1/hypervisors":["sysadm"], 
    "/v1/clusters":["sysadm"], 
    "/v1/id/clusters/\d+":["sysadm", "operator"], 
    "/v1/home":["sysadm","secadm", "adtadm", "operator"], 
    "/v1/add/clusters":["sysadm"], 
    "/v1/clusters/create":["sysadm"], 
    "/v1/delete/clusters/host":["sysadm"], 
    "/v1/update/clusters/name":["sysadm"], 
    "/v1/server/iso":["sysadm"], 
    "/v1/instance/delete":["sysadm", "operator"], 
    "/v1/instances":["sysadm", "operator"], 
    "/v1/instances/hosts":["sysadm", "operator"], 
    "/v1/instances/detail":["sysadm", "operator"], 
    "/v1/instance/create":["sysadm", "operator"], 
    "/v1/instances/detach":["sysadm", "operator"], 
    "/v1/instance/action":["sysadm", "operator"], 
    "/v1/instance/detail":["sysadm", "operator"], 
    "/v1/instance/snapshot":["sysadm", "operator"], 
    "/v1/instance/edit":["sysadm", "operator"], 
    "/v1/instance/unmountiso":["sysadm", "operator"],
    "/v1/recycle/list":["sysadm", "operator"],
    "/v1/instance/isocreate":["sysadm", "operator"],
    "/v1/instances/detach":["sysadm", "operator"],
    "/v1/instances/attach":["sysadm", "operator"], 
    "/v1/availablevolumes":["sysadm", "operator"], 
    "/v1/garnt/instances":["sysadm", "operator"], 
    "/v1/garnt/instances/to/user":["sysadm", "operator"], 
    "/v1/chart/host/cpu":["sysadm", "operator"], 
    "/v1/chart/host/mem":["sysadm", "operator"], 
    "/v1/chart/host/net":["sysadm", "operator"], 
    "/v1/chart/vm/mem":["sysadm", "operator"], 
    "/v1/chart/vm/cpu":["sysadm", "operator"], 
    "/v1/chart/vm/net":["sysadm", "operator"], 
    "/v1/networks/delete":["sysadm", "operator"], 
    "/v1/physical/networks/delete":["sysadm", "operator"], 
    "/v1/networks":["sysadm", "operator"], 
    "/v1/networks/physical":["sysadm", "operator"], 
    "/v1/create/sysadmnetworks":["sysadm"], 
    "/v1/create/networks":["sysadm","operator"], 
    "/v1/create/physical/networks":["sysadm", "operator"], 
    "/v1/networks/edit":["sysadm", "operator"], 
    "/v1/physical/networks/update":["sysadm", "operator"], 
    "/v1/images/delete":["sysadm"], 
    "/v1/images":["sysadm", "operator"], 
    "/v1/images/create":["sysadm", "operator"], 
    "/v1/images/edit":["sysadm", "operator"], 
    "/v1/volumes/delete":["sysadm", "operator"], 
    "/v1/volumes/type":["sysadm", "operator"], 
    "/v1/volumes":["sysadm", "operator"], 
    "/v1/volumes/attach":["sysadm", "operator"], 
    "/v1/volumes/create":["sysadm", "operator"], 
    "/v1/volumes/action":["sysadm", "operator"], 
    "/v1/volumes/backup":["sysadm", "operator"], 
    "/v1/volumes/detail":["sysadm", "operator"], 
    "/v1/volumes/snapshot":["sysadm", "operator"], 
    "/v1/volumes/edit":["sysadm", "operator"], 
    "/v1/flavors/delete/{id}":["sysadm", "operator"], 
    "/v1/flavors":["sysadm", "operator"], 
    "/v1/flavors/create":["sysadm", "operator"], 
    "/v1/flavors/detail":["sysadm", "operator"], 
    "/v1/event/query":["sysadm"], 
    "/v1/ceph/all":["sysadm"], 
    "/v1/delete/groups":["sysadm", "operator"], 
    "/v1/groups/remove":["sysadm", "operator"], 
    "/v1/groups":["sysadm", "operator"], 
    "/v1/groups/create":["sysadm", "operator"], 
    "/v1/groups/instances":["sysadm", "operator"], 
    "/v1/groups/rename":["sysadm", "operator"], 
    "/v1/groups/add":["sysadm", "operator"],
    "/v1/user/delete":["sysadm", "secadm"], 
    "/v1/user/all":["sysadm", "secadm", "adtadm", "operator"], 
    "/v1/user/add":["sysadm", "secadm"], 
    "/v1/user/changepassword":["sysadm", "secadm", "adtadm", "operator"], 
    "/v1/user/resetpassword":["sysadm", "secadm"], 
    "/v1/user/status":["sysadm", "secadm", "adtadm"], 
    "/v1/user/update":["sysadm", "secadm"],
    "/v1/images/upload": ["sysadm", "operator"],
    "/v1/images/create": ["sysadm", "operator"]
}

# thecss config
AMQP_URI = "pyamqp://openstack:openstack@rabbitmq"
HOST_DATA_KEY = {
    "node_cpu_seconds_total",  
    "node_cpu_usage",  
    "node_cpu_count", 
    "node_memory_usage", 
    "node_memory_total",
    "vm_process_cpu_seconds",
    "vm_cpu_count",
    "vm_process_memory",
    "vm_memory_maximum",
    "vm_usable_memory",
    "vm_memory_usage",
    "vm_status"
    "cluster_host"
}
HOST_JOB = "node-exporter"
VM_JOB = "qemu"