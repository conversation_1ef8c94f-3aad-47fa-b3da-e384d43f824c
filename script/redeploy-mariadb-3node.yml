---
- name: deploy mariadb
  hosts: managers
  vars:
    src_docker_compose: docker-compose.yml
    dest_docker_compose: /etc/thestack/compose/docker-compose-mariadb.yml
    stack_name: mariadb
    is_remove: "{{ remove }}"
  tasks:

    - name: Print install_status
      debug:
        msg: "{{ is_remove }}"

    - name: install mariadb
      include_role:
        name: "../roles/mariadb-3node"
        tasks_from: redeploy
      when:  is_remove == 'false'

    - name: uninstall mariadb
      shell: docker stack rm mariadb
      when: "is_remove == 'true'"
      run_once: true  
