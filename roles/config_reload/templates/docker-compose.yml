version: "3.7"


configs:
  traefik_router.yml:
    file: /etc/thestack/conf/traefik/tcp_routers.yml
  mariadb_init.sql:
    file: /etc/thestack/conf/mariadb/init.sql
  redis.conf:
    file: /etc/thestack/conf/redis/redis.conf
  prometheus.yml:
    file: /etc/thestack/conf/prometheus/prometheus.yml
  alertmanager.yml:
    file: /etc/thestack/conf/alertmanager/alertmanager.yml
  alertmanager_template_default.tmpl:
    file: /etc/thestack/conf/alertmanager/default.tmpl
  fluent.conf:
    file: /etc/thestack/conf/efk/fluent.conf
  grafana.ini:
    file: /etc/thestack/conf/grafana/grafana.ini
  theauth.ini:
    file: /etc/thestack/conf/theauth/config.ini
  theweb.py:
    file: /etc/thestack/conf/theweb/settings.py
  conductor.yml:
    file: /etc/thestack/conf/dayu/conductor.yml
  executor.yml:
    file: /etc/thestack/conf/dayu/executor.yml
  recommend.yml:
    file: /etc/thestack/conf/dayu/recommend.yml
  scheduler.yml:
    file: /etc/thestack/conf/dayu/scheduler.yml
  trigger.yml:
    file: /etc/thestack/conf/dayu/trigger.yml
  thecephapi.yml:
    file: /etc/thestack/conf/thecephapi/config.yml
  keepalived.conf:
    file: /etc/thestack/conf/keeplived/keeplived.conf
  haproxy.cfg:
    file: /etc/thestack/conf/haproxy/haproxy.cfg