---
- name: Ensure rabbitmq config directory exists
  file:
    path: "{{ dest_rabbitmq_conf | dirname }}"
    state: directory
    mode: '0755'
    recurse: yes


- name: upload rabbitmq conf
  template:
    src: "{{ src_rabbitmq_conf }}"
    dest: "{{ dest_rabbitmq_conf }}"
    force: yes
    mode: '0644'    # 可选：设置文件权限
    backup: yes     # 可选：在覆盖前创建备份
  with_items: "{{ groups['managers'] }}"

- name: upload rabbitmq plugin conf
  template:
    src: "{{ src_rabbitmq_enabled_plugins }}"
    dest: "{{ dest_rabbitmq_enabled_plugins }}"
    force: yes
    mode: '0644'    # 可选：设置文件权限
    backup: yes     # 可选：在覆盖前创建备份
  with_items: "{{ groups['managers'] }}"



- name: Upload docker compose file
  template:
    src: "{{ src_docker_compose }}"
    dest: "{{ dest_docker_compose }}"
    force: yes
    mode: '0644'    # 可选：设置文件权限
    backup: yes     # 可选：在覆盖前创建备份

- name: Add RabbitMQ node labels
  shell: "docker node update --label-add rabbitmq_node={{ idx + 1 }} {{ item }}"
  with_items: "{{ groups['managers'] }}"
  loop_control:
    index_var: idx
  run_once: true

# - name: Add RabbitMQ node labels
#   shell: "{{ item }}"
#   with_items:
#     - "docker node update --label-add rabbitmq_node=1 controller1"
#     - "docker node update --label-add rabbitmq_node=2 controller2"
#     - "docker node update --label-add rabbitmq_node=3 controller3"
#   run_once: true

- name: Ensure prod network exists
  ansible.builtin.shell: |
    if ! docker network ls | grep -q prod; then
      docker network create -d overlay prod
    fi


- name: Deploy services
  shell: docker stack deploy -c {{ dest_docker_compose }} {{ stack_name }}
  run_once: true

- name: Pause for 10 seconds to wait for services being running
  pause:
    seconds: 10
  run_once: true

- name: Ensure services deployed
  shell: docker service ls
  register: service_output

- name: Output service list
  debug:
    msg: "{{ service_output.stdout_lines }}"

- name: Show stacks
  shell: docker stack list