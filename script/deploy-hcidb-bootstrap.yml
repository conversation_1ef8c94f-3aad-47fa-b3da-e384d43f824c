---
- name: deploy hcidb-bootstrap
  hosts: deploy
  vars:
    src_docker_compose: docker-compose.yml
    dest_docker_compose: /etc/thestack/compose/docker-compose-hcidb-bootstrap.yml
    src_hcidb_settings: settings.py.j2
    dest_hcidb_settings: /etc/thestack/conf/hcidb/settings.py

    stack_name: hcidb-bootstrap
    is_remove: "{{ remove }}"
  tasks:

    - name: Print install_status
      debug:
        msg: "{{ is_remove }}"

    - name: install hcidb-bootstrap
      include_role:
        name: "../roles/hcidb-bootstrap"
      when:  is_remove == 'false'

    - name: uninstall hcidb-bootstrap
      shell: docker stack rm hcidb-bootstrap
      when: "is_remove == 'true'"
      run_once: true  
