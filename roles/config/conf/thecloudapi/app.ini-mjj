[app]
PageSize = 10
JwtSecret = 233
PrefixUrl = http://127.0.0.1:8000

RuntimeRootPath = runtime/

ImageSavePath = upload/images/
# MB
ImageMaxSize = 5
ImageAllowExts = .jpg,.jpeg,.png

ExportSavePath = export/
QrCodeSavePath = qrcode/
FontSavePath = fonts/

LogSavePath = logs/
LogSaveName = log
LogFileExt = log
TimeFormat = 20060102

[server]
#debug or release
RunMode = debug
HttpPort = 8007
ReadTimeout = 60
WriteTimeout = 60

[database]
Type = mysql
User = root
Password = rootroot
Host = 127.0.0.1:3306
Name = blog
TablePrefix = blog_

[redis]
Host = 127.0.0.1:6379
Password =
MaxIdle = 30
MaxActive = 30
IdleTimeout = 200

[etcd]
EtcdUrl = ***************:2379

[keystone]
AuthUri = http://***************:5000/v3
AuthType = password
ProjectDomainId = default
UserDomainId = default
ProjectName = admin
Username = admin
Password = thecloud2015.1