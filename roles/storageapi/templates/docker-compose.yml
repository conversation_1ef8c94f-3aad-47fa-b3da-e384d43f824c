version: "3.7"
services:
  storageapi:
    hostname: storageapi
    image: tianwen1:5000/storageapi:latest
    configs:
      - source: storageapi.ini
        target: /opt/storageapi/conf/app.ini
    networks:
      - traefik_prod
    deploy:
      mode: replicated
      replicas: 1
      restart_policy:
        condition: any
        delay: 10s
        window: 120s
      placement:
        constraints:
          - node.role==manager
      labels:
        - traefik.enable=true
        - traefik.http.middlewares.storageapi_strip.stripprefix.prefixes=/storageapi
        - traefik.http.routers.storageapi.rule=PathPrefix(`/storageapi`)
        - traefik.http.routers.storageapi.entrypoints=web
        - traefik.http.services.storageapi.loadbalancer.server.port=8055
        - traefik.http.routers.storageapi.middlewares=storageapi_strip
        - traefik.docker.network=traefik_prod

networks:
  traefik_prod:
    external: true

configs:
  storageapi.ini:
    file: /etc/thestack/conf/storageapi/storageapi.ini
