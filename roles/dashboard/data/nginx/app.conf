server {
        listen       8008 default_server;
        server_name  127.0.0.1;
        client_max_body_size  200000m;
        #proxy_request_buffering off;
        #sendfile  on;
        #keepalive_timeout   300;
        #tcp_nodelay   on;
        
        
        # Load configuration files for the default server block.
        #include /etc/nginx/default.d/*.conf;
                charset utf-8;
        location / {
            root   html;
            index  index.html index.htm;

        }

        error_page 404 /404.html;
            location = /40x.html {
        }

        error_page 500 502 503 504 /50x.html;
            location = /50x.html {
        }


        location /api/ {
            proxy_pass http://127.0.0.1:4455;
        }

        location /upload/ {
            client_max_body_size 200000m;
            #client_body_buffer_size 10m;
    	    proxy_request_buffering off;

            proxy_pass http://127.0.0.1:8090/;
        }
        
        location /user/ {
            proxy_pass http://127.0.0.1:4455;
        }

        location /cloudy/ {
            proxy_pass http://127.0.0.1:4455;
        }
        
        location /log/ {
            proxy_pass http://127.0.0.1:4455;
        }
        
        location /desk/ {
   			proxy_http_version 1.1;
    		proxy_set_header Upgrade $http_upgrade;
    		proxy_set_header Connection "Upgrade";
            proxy_pass http://127.0.0.1:4455/;
        }


        location /login/ {
            proxy_pass http://127.0.0.1:8088/;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            
        }
        
        location /login {
            proxy_pass http://127.0.0.1:8088/;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }


        location /the {
            proxy_pass http://127.0.0.1:8066/;
        }

        location /the/ {
            proxy_pass http://127.0.0.1:8066/;
        }

}