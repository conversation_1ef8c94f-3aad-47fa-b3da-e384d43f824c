version: '3.8'

services:
  keepalived-master:
    image: tianwen1:5000/my-keepalived:latest
    networks:
      - hostnet
    cap_add:
      - NET_ADMIN
    volumes:
      - /lib/modules:/lib/modules:ro
    configs:
      - source: keepalived-master.conf
        target: /etc/keepalived/keepalived.conf
    deploy:
      mode: global
      placement:
        constraints:
          - node.hostname == controller1
    environment:
      # - KEEPALIVED_UNICAST_PEERS="#PYTHON2BASH:['***************', '***************', '***************']"
      - KEEPALIVED_UNICAST_PEERS="#PYTHON2BASH:{{ groups['docker-nodes'] | map('extract', hostvars, ['ansible_host']) | list }}"
      - KEEPALIVED_VIRTUAL_IPS="#PYTHON2BASH:['{{ vip }}/24']"
      - KEEPALIVED_PRIORITY=200

  keepalived-back1:
    image: tianwen1:5000/my-keepalived:latest
    networks:
      - hostnet
    cap_add:
      - NET_ADMIN
    volumes:
      - /lib/modules:/lib/modules:ro
    configs:
      - source: keepalived-backup1.conf
        target: /etc/keepalived/keepalived.conf
    deploy:
      mode: global
      placement:
        constraints:
          - node.hostname == controller2
    environment:
      # - KEEPALIVED_UNICAST_PEERS="#PYTHON2BASH:['***************', '***************', '***************']"
      - KEEPALIVED_UNICAST_PEERS="#PYTHON2BASH:{{ groups['docker-nodes'] | map('extract', hostvars, ['ansible_host']) | list }}"
      - KEEPALIVED_VIRTUAL_IPS="#PYTHON2BASH:['{{ vip }}/24']"
      - KEEPALIVED_PRIORITY=200
      
  keepalived-back2:
    image: tianwen1:5000/my-keepalived:latest
    networks:
      - hostnet
    cap_add:
      - NET_ADMIN
    volumes:
      - /lib/modules:/lib/modules:ro
    configs:
      - source: keepalived-backup2.conf
        target: /etc/keepalived/keepalived.conf
    deploy:
      mode: global
      placement:
        constraints:
          - node.hostname == controller3
    environment:
      # - KEEPALIVED_UNICAST_PEERS="#PYTHON2BASH:['***************', '***************', '***************']"
      - KEEPALIVED_UNICAST_PEERS="#PYTHON2BASH:{{ groups['docker-nodes'] | map('extract', hostvars, ['ansible_host']) | list }}"
      - KEEPALIVED_VIRTUAL_IPS="#PYTHON2BASH:['{{ vip }}/24']"
      - KEEPALIVED_PRIORITY=200
      
networks:
  hostnet:
    external: true
    name: host

configs:
  keepalived-master.conf:
    file: /etc/thestack/conf/keeplived/keeplived-master.conf
  keepalived-backup1.conf:
    file: /etc/thestack/conf/keeplived/keeplived-backup1.conf
  keepalived-backup2.conf:
    file: /etc/thestack/conf/keeplived/keeplived-backup2.conf
