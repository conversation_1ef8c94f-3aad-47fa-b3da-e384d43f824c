---
- name: Create directory
  file:
    path: "{{ item }}"
    state: directory
    mode: 0777
  with_items:
    - /mnt/ceph/redismaster/data
    - /mnt/ceph/redismaster/etc
    - /mnt/ceph/redismaster/log
    - /mnt/ceph/redisslave/data
    - /mnt/ceph/redisslave/etc
    - /mnt/ceph/redisslave/log
    - /etc/thestack/conf/redis

- name: Create Redis configuration file
  template:
    src: "redis.conf.j2"
    dest: "/etc/thestack/conf/redis/redis.conf"
    mode: '0644'
    backup: yes

- name: Set system memory overcommit
  sysctl:
    name: vm.overcommit_memory
    value: '1'
    state: present
    reload: yes

- name: Upload docker compose file
  template:
    src: "{{ src_docker_compose }}"
    dest: "{{ dest_docker_compose }}"
    force: yes
    mode: '0644'
    backup: yes

- name: Deploy services
  shell: docker stack deploy -c {{ dest_docker_compose }} {{ stack_name }}
  run_once: true

- name: Pause for 2 seconds to wait for services being running
  pause:
    seconds: 2
  run_once: true

- name: Ensure services deployed
  shell: docker service ls
  register: service_output

- name: Output service list
  debug:
    msg: "{{ service_output.stdout_lines }}"

- name: Show stacks
  shell: docker stack list
