---
- name: Create multiple directories
  file:
    path: "/etc/thestack/conf/{{ item }}"
    state: directory 
    mode: '0755'
    recurse: yes
  with_items:
    - grafana

- name: Upload configuration files from templates
  template:
    src: "{{ item.dir }}/{{ item.file }}"
    dest: "/etc/thestack/conf/{{ item.dir }}/{{ item.file }}"
    force: yes
    mode: '0644'
    backup: yes
  loop:
    - { dir: 'grafana', file: 'grafana.ini' }


- name: Upload docker compose file
  template:
    src: "{{ src_docker_compose }}"
    dest: "{{ dest_docker_compose }}"
    force: yes
    mode: '0644'    # 可选：设置文件权限
    backup: yes     # 备份

- name: Deploy services
  shell: docker stack deploy -c {{ dest_docker_compose }} {{ stack_name }}
  run_once: true

- name: Pause for 2 seconds to wait for services being running
  pause:
    seconds: 2
  run_once: true

- name: Ensure services deployed
  shell: docker service ls
  register: service_output

- name: Output service list
  debug:
    msg: "{{ service_output.stdout_lines }}"

- name: Show stacks
  shell: docker stack list
  
- include_tasks: token.yml