[Unit]
Description=disk-exporter

After=network-online.target local-fs.target time-sync.target
Wants=network-online.target local-fs.target time-sync.target


[Service]
LimitNOFILE=1048576
LimitNPROC=1048576
EnvironmentFile=-/etc/environment
ExecStartPre=-/usr/bin/docker rm disk-exporter
ExecStart=-/usr/bin/docker run  --name disk-exporter --net=host --privileged -v /proc:/host/proc -v /sys:/host/sys -v /:/rootfs -v /etc:/host/etc -v /dev:/dev -v /run/lvm:/run/lvm -t  -v /var/run/docker.sock:/var/run/docker.sock tianwen1:5000/theexporter:latest
ExecStop=-/usr/bin/docker stop disk-exporter
KillMode=none
Restart=on-failure
RestartSec=10s
TimeoutStartSec=120
TimeoutStopSec=120
StartLimitInterval=30min
StartLimitBurst=5

[Install]
WantedBy=default.target