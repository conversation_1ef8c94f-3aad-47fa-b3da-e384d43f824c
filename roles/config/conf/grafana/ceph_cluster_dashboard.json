{"dashboard": {"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "description": "Ceph Cluster overview.\r\n", "editable": true, "gnetId": 2842, "graphTooltip": 0, "id": null, "iteration": 1687762525146, "links": [], "panels": [{"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 37, "panels": [], "repeat": null, "title": "CLUSTER STATE", "type": "row"}, {"cacheTimeout": null, "datasource": "Prometheus", "fieldConfig": {"defaults": {"mappings": [{"id": 0, "op": "=", "text": "N/A", "type": 1, "value": "null"}, {"id": 1, "op": "=", "text": "WARNING", "type": 1, "value": "1"}, {"id": 2, "op": "=", "text": "HEALTHY", "type": 1, "value": "0"}, {"id": 3, "op": "=", "text": "ERROR", "type": 1, "value": "2"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#9ac48a", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 1}, {"color": "rgba(245, 54, 54, 0.9)", "value": 2}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 0, "y": 1}, "id": 21, "interval": "1m", "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "center", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.2", "targets": [{"expr": "sum without (instance) (ceph_health_status{cluster=\"$cluster\"})", "format": "time_series", "instant": true, "interval": "$interval", "intervalFactor": 1, "legendFormat": " ", "refId": "A", "step": 300}], "transparent": true, "type": "stat"}, {"cacheTimeout": null, "datasource": "Prometheus", "fieldConfig": {"defaults": {"decimals": 1, "mappings": [{"id": 0, "op": "=", "text": "N/A", "type": 1, "value": "null"}], "thresholds": {"mode": "absolute", "steps": [{"color": "rgb(255, 255, 255)", "value": null}]}, "unit": "Bps"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 3, "y": 1}, "id": 92, "interval": null, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.2", "targets": [{"expr": "sum without (instance, ceph_daemon) (irate(ceph_osd_op_w_in_bytes{cluster=\"$cluster\"}[5m]))", "format": "time_series", "instant": true, "interval": "$interval", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Write Throughput", "type": "stat"}, {"cacheTimeout": null, "datasource": "Prometheus", "fieldConfig": {"defaults": {"decimals": 1, "mappings": [{"id": 0, "op": "=", "text": "N/A", "type": 1, "value": "null"}], "thresholds": {"mode": "absolute", "steps": [{"color": "rgb(255, 255, 255)", "value": null}]}, "unit": "Bps"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 6, "y": 1}, "id": 93, "interval": null, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.2", "targets": [{"expr": "sum without (ceph_daemon, instance) (irate(ceph_osd_op_r_out_bytes{cluster=\"$cluster\"}[5m]))", "format": "time_series", "instant": true, "interval": "$interval", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Read Throughput", "type": "stat"}, {"cacheTimeout": null, "datasource": "Prometheus", "fieldConfig": {"defaults": {"mappings": [{"id": 0, "op": "=", "text": "N/A", "type": 1, "value": "null"}], "thresholds": {"mode": "absolute", "steps": [{"color": "rgba(255, 255, 255, 0.97)", "value": null}]}, "unit": "decbytes"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 9, "y": 1}, "id": 33, "interval": "1m", "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.2", "targets": [{"expr": "sum without (instance) (ceph_cluster_total_bytes{cluster=\"$cluster\"})", "format": "time_series", "instant": true, "interval": "$interval", "intervalFactor": 1, "legendFormat": "", "refId": "A", "step": 300}], "title": "Cluster Capacity", "type": "stat"}, {"cacheTimeout": null, "datasource": "Prometheus", "fieldConfig": {"defaults": {"mappings": [{"id": 0, "op": "=", "text": "N/A", "type": 1, "value": "null"}], "max": 1, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "rgba(245, 54, 54, 0.9)", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 0.1}, {"color": "rgba(50, 172, 45, 0.97)", "value": 0.3}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 6, "w": 3, "x": 12, "y": 1}, "id": 23, "interval": "1m", "links": [], "maxDataPoints": 100, "options": {"orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "text": {}}, "pluginVersion": "7.5.2", "targets": [{"expr": "sum without (instance) ((ceph_cluster_total_bytes{cluster=\"$cluster\"}-ceph_cluster_total_used_bytes{cluster=\"$cluster\"})/ceph_cluster_total_bytes{cluster=\"$cluster\"})", "format": "time_series", "instant": true, "interval": "$interval", "intervalFactor": 1, "legendFormat": "", "refId": "A", "step": 300}], "title": "Available Capacity", "type": "gauge"}, {"cacheTimeout": null, "datasource": "Prometheus", "fieldConfig": {"defaults": {"mappings": [{"id": 0, "op": "=", "text": "N/A", "type": 1, "value": "null"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "dark-yellow", "value": 75000000}, {"color": "dark-red", "value": 100000000}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 15, "y": 1}, "id": 48, "interval": null, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.2", "targets": [{"expr": "sum without (instance, pool_id) (ceph_pool_objects{cluster=\"$cluster\"})", "format": "time_series", "instant": true, "interval": "$interval", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Number of Objects", "type": "stat"}, {"cacheTimeout": null, "datasource": "Prometheus", "fieldConfig": {"defaults": {"decimals": 1, "mappings": [{"id": 0, "op": "=", "text": "N/A", "type": 1, "value": "null"}], "thresholds": {"mode": "absolute", "steps": [{"color": "rgb(255, 255, 255)", "value": null}]}, "unit": "decbytes"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 18, "y": 1}, "id": 99, "interval": null, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["delta"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.2", "targets": [{"expr": "sum without (instance, ceph_daemon) (ceph_osd_op_w_in_bytes{cluster=\"$cluster\"})", "format": "time_series", "instant": false, "interval": "$interval", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Bytes Written", "type": "stat"}, {"cacheTimeout": null, "datasource": "Prometheus", "fieldConfig": {"defaults": {"decimals": 1, "mappings": [{"id": 0, "op": "=", "text": "N/A", "type": 1, "value": "null"}], "thresholds": {"mode": "absolute", "steps": [{"color": "rgb(255, 255, 255)", "value": null}]}, "unit": "decbytes"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 21, "y": 1}, "id": 100, "interval": null, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["delta"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.2", "targets": [{"expr": "sum without (instance, ceph_daemon) (ceph_osd_op_r_out_bytes{cluster=\"$cluster\"})", "format": "time_series", "instant": false, "interval": "$interval", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "<PERSON><PERSON>", "type": "stat"}, {"cacheTimeout": null, "datasource": "Prometheus", "fieldConfig": {"defaults": {"mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "#9ac48a", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 1}, {"color": "#e24d42", "value": 1}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 0, "y": 4}, "id": 75, "interval": null, "links": [], "maxDataPoints": 100, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.2", "targets": [{"expr": "count(ALERTS{cluster='$cluster',alertstate='firing',alertname=~'^CephCluster.*'}) OR vector(0)", "format": "time_series", "instant": true, "interval": "", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "<PERSON><PERSON><PERSON>", "transparent": true, "type": "stat"}, {"cacheTimeout": null, "datasource": "Prometheus", "fieldConfig": {"defaults": {"decimals": 2, "mappings": [{"id": 0, "op": "=", "text": "N/A", "type": 1, "value": "null"}], "thresholds": {"mode": "absolute", "steps": [{"color": "rgb(255, 255, 255)", "value": null}]}, "unit": "ops"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 3, "y": 4}, "id": 97, "interval": null, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.2", "targets": [{"expr": "sum without (ceph_daemon, instance) (irate(ceph_osd_op_w{cluster=\"$cluster\"}[5m]))", "format": "time_series", "instant": true, "interval": "$interval", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Write IOPS", "type": "stat"}, {"cacheTimeout": null, "datasource": "Prometheus", "fieldConfig": {"defaults": {"decimals": 2, "mappings": [{"id": 0, "op": "=", "text": "N/A", "type": 1, "value": "null"}], "thresholds": {"mode": "absolute", "steps": [{"color": "rgb(255, 255, 255)", "value": null}]}, "unit": "ops"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 6, "y": 4}, "id": 96, "interval": null, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.2", "targets": [{"expr": "sum without (ceph_daemon, instance) (irate(ceph_osd_op_r{cluster=\"$cluster\"}[5m]))", "format": "time_series", "instant": true, "interval": "$interval", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Read IOPS", "type": "stat"}, {"cacheTimeout": null, "datasource": "Prometheus", "fieldConfig": {"defaults": {"mappings": [{"id": 0, "op": "=", "text": "N/A", "type": 1, "value": "null"}], "thresholds": {"mode": "absolute", "steps": [{"color": "rgba(255, 255, 255, 0.97)", "value": null}]}, "unit": "decbytes"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 9, "y": 4}, "id": 34, "interval": "1m", "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.2", "targets": [{"expr": "sum without (instance) (ceph_cluster_total_used_bytes{cluster=\"$cluster\"})", "format": "time_series", "instant": true, "interval": "$interval", "intervalFactor": 1, "legendFormat": "", "refId": "A", "step": 300}], "title": "Used Capacity", "type": "stat"}, {"cacheTimeout": null, "datasource": "Prometheus", "fieldConfig": {"defaults": {"mappings": [{"id": 0, "op": "=", "text": "N/A", "type": 1, "value": "null"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 18, "y": 4}, "id": 102, "interval": null, "links": [], "maxDataPoints": 100, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.2", "targets": [{"expr": "sum without (instance, ceph_daemon) (ceph_mon_num_sessions{cluster='$cluster'})", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Mon Session Num", "type": "stat"}, {"cacheTimeout": null, "datasource": "Prometheus", "fieldConfig": {"defaults": {"mappings": [{"id": 0, "op": "=", "text": "N/A", "type": 1, "value": "null"}], "thresholds": {"mode": "absolute", "steps": [{"color": "rgba(245, 54, 54, 0.9)", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 2}, {"color": "green", "value": 3}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 21, "y": 4}, "id": 14, "interval": "1m", "links": [], "maxDataPoints": 100, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.2", "targets": [{"expr": "count without (instance, ceph_daemon) (ceph_mon_quorum_status{cluster=\"$cluster\"})", "format": "time_series", "instant": true, "interval": "$interval", "intervalFactor": 1, "legendFormat": "", "refId": "A", "step": 300}], "title": "Monitors In Quorum", "type": "stat"}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 7}, "id": 38, "panels": [], "repeat": null, "title": "OSD STATE", "type": "row"}, {"cacheTimeout": null, "datasource": "Prometheus", "fieldConfig": {"defaults": {"mappings": [{"id": 0, "op": "=", "text": "N/A", "type": 1, "value": "null"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#9ac48a", "value": null}, {"color": "rgba(237, 40, 40, 0.89)", "value": 1}, {"color": "rgba(245, 54, 54, 0.9)", "value": 1}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 3, "w": 2, "x": 0, "y": 8}, "id": 27, "interval": null, "links": [], "maxDataPoints": 100, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.2", "targets": [{"expr": "count without (instance, ceph_daemon) (ceph_osd_up{cluster=\"$cluster\"}) - count without (instance, ceph_daemon) (ceph_osd_in{cluster=\"$cluster\"})", "format": "time_series", "instant": true, "interval": "$interval", "intervalFactor": 1, "legendFormat": "", "refId": "A", "step": 300}], "title": "OSDs OUT", "type": "stat"}, {"cacheTimeout": null, "datasource": "Prometheus", "fieldConfig": {"defaults": {"mappings": [{"id": 0, "op": "=", "text": "N/A", "type": 1, "value": "null"}], "thresholds": {"mode": "absolute", "steps": [{"color": "rgba(50, 172, 45, 0.97)", "value": null}, {"color": "#eab839", "value": 1}, {"color": "#ea6460", "value": 1}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 3, "w": 2, "x": 2, "y": 8}, "id": 29, "interval": null, "links": [], "maxDataPoints": 100, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.2", "targets": [{"expr": "count(ceph_osd_up{cluster=\"$cluster\"} == 0.0) OR vector(0)", "format": "time_series", "instant": true, "interval": "$interval", "intervalFactor": 1, "legendFormat": "", "refId": "A", "step": 300}], "title": "OSDs DOWN", "type": "stat"}, {"cacheTimeout": null, "datasource": "Prometheus", "fieldConfig": {"defaults": {"mappings": [{"id": 0, "op": "=", "text": "N/A", "type": 1, "value": "null"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 3, "w": 2, "x": 4, "y": 8}, "id": 28, "interval": null, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.2", "targets": [{"expr": "sum without (instance, ceph_daemon) (ceph_osd_up{cluster=\"$cluster\"})", "format": "time_series", "instant": true, "interval": "$interval", "intervalFactor": 1, "legendFormat": "", "refId": "A", "step": 300}], "title": "OSDs UP", "type": "stat"}, {"cacheTimeout": null, "datasource": "Prometheus", "fieldConfig": {"defaults": {"mappings": [{"id": 0, "op": "=", "text": "N/A", "type": 1, "value": "null"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 3, "w": 2, "x": 6, "y": 8}, "id": 26, "interval": null, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.2", "targets": [{"expr": "sum without (instance, ceph_daemon) (ceph_osd_in{cluster=\"$cluster\"})", "format": "time_series", "instant": true, "interval": "$interval", "intervalFactor": 1, "legendFormat": "", "refId": "A", "step": 300}], "title": "OSDs IN", "type": "stat"}, {"cacheTimeout": null, "datasource": "Prometheus", "fieldConfig": {"defaults": {"mappings": [{"id": 0, "op": "=", "text": "N/A", "type": 1, "value": "null"}], "thresholds": {"mode": "absolute", "steps": [{"color": "rgba(50, 172, 45, 0.97)", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 250}, {"color": "rgba(245, 54, 54, 0.9)", "value": 300}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 3, "w": 2, "x": 8, "y": 8}, "id": 30, "interval": null, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.2", "targets": [{"expr": "avg without (instance, ceph_daemon) (ceph_osd_numpg{cluster=\"$cluster\"})", "format": "time_series", "instant": true, "interval": "$interval", "intervalFactor": 1, "legendFormat": "", "refId": "A", "step": 300}], "title": "Avg PGs", "type": "stat"}, {"cacheTimeout": null, "datasource": "Prometheus", "fieldConfig": {"defaults": {"mappings": [{"id": 0, "op": "=", "text": "N/A", "type": 1, "value": "null"}], "thresholds": {"mode": "absolute", "steps": [{"color": "rgba(50, 172, 45, 0.97)", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 10}, {"color": "rgba(245, 54, 54, 0.9)", "value": 50}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 10, "y": 8}, "id": 31, "interval": null, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.2", "targets": [{"expr": "avg without (instance, ceph_daemon) (ceph_osd_apply_latency_ms{cluster=\"$cluster\"})", "format": "time_series", "instant": true, "interval": "$interval", "intervalFactor": 1, "legendFormat": "", "refId": "A", "step": 300}], "title": "Avg Apply Latency", "type": "stat"}, {"cacheTimeout": null, "datasource": "Prometheus", "fieldConfig": {"defaults": {"mappings": [{"id": 0, "op": "=", "text": "N/A", "type": 1, "value": "null"}], "thresholds": {"mode": "absolute", "steps": [{"color": "rgba(50, 172, 45, 0.97)", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 10}, {"color": "rgba(245, 54, 54, 0.9)", "value": 50}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 13, "y": 8}, "id": 32, "interval": null, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.2", "targets": [{"expr": "avg(ceph_osd_commit_latency_ms{cluster=\"$cluster\"})", "format": "time_series", "instant": true, "interval": "$interval", "intervalFactor": 1, "legendFormat": "", "refId": "A", "step": 300}], "title": "Avg Commit Latency", "type": "stat"}, {"cacheTimeout": null, "datasource": "Prometheus", "fieldConfig": {"defaults": {"mappings": [{"id": 0, "op": "=", "text": "0", "type": 1, "value": "null"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#299c46", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 1}, {"color": "#d44a3a", "value": 2}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 16, "y": 8}, "id": 51, "interval": null, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.2", "targets": [{"expr": "avg without (instance, ceph_daemon) (rate(ceph_osd_op_w_latency_sum{cluster=\"$cluster\"}[5m]) / rate(ceph_osd_op_w_latency_count{cluster=\"$cluster\"}[5m]) >= 0)", "format": "time_series", "instant": false, "interval": "", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Avg Op Write Latency", "type": "stat"}, {"cacheTimeout": null, "datasource": "Prometheus", "fieldConfig": {"defaults": {"mappings": [{"id": 0, "op": "=", "text": "N/A", "type": 1, "value": "null"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#299c46", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 1}, {"color": "#d44a3a", "value": 2}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 19, "y": 8}, "id": 50, "interval": null, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.2", "targets": [{"expr": "avg without (instance, ceph_daemon) (rate(ceph_osd_op_r_latency_sum{cluster=\"$cluster\"}[5m])/rate(ceph_osd_op_r_latency_count{cluster=\"$cluster\"}[5m]) >= 0)", "format": "time_series", "instant": true, "interval": "", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Avg  Op Read Latency", "type": "stat"}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 11}, "id": 53, "panels": [{"columns": [], "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fontSize": "100%", "gridPos": {"h": 6, "w": 8, "x": 0, "y": 12}, "id": 70, "links": [], "pageSize": null, "scroll": true, "showHeader": true, "sort": {"col": 0, "desc": true}, "styles": [{"alias": "Time", "align": "auto", "dateFormat": "YYYY-MM-DD HH:mm:ss", "pattern": "Time", "type": "date"}, {"alias": "", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "decimals": 2, "pattern": "/.*/", "thresholds": [], "type": "number", "unit": "short"}], "targets": [{"expr": "ALERTS{cluster='$cluster', alertstate='firing'}", "format": "table", "instant": true, "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Alerts from CephThanos", "transform": "table", "type": "table-old"}, {"columns": [], "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fontSize": "100%", "gridPos": {"h": 6, "w": 8, "x": 8, "y": 12}, "id": 105, "links": [], "pageSize": null, "scroll": true, "showHeader": true, "sort": {"col": 5, "desc": true}, "styles": [{"alias": "Time", "align": "auto", "dateFormat": "YYYY-MM-DD HH:mm:ss", "pattern": "Time", "type": "date"}, {"alias": "", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "decimals": 2, "pattern": "/.*/", "thresholds": [], "type": "number", "unit": "short"}], "targets": [{"expr": "topk(5,sort_desc(ceph_osd_apply_latency_ms{cluster='$cluster'} + ceph_osd_commit_latency_ms{cluster='$cluster'}))", "format": "table", "instant": true, "intervalFactor": 1, "refId": "A", "target": ""}], "title": "Top Sluggish OSD's", "transform": "table", "type": "table-old"}, {"columns": [], "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fontSize": "100%", "gridPos": {"h": 6, "w": 8, "x": 16, "y": 12}, "id": 103, "links": [], "pageSize": null, "scroll": true, "showHeader": true, "sort": {"col": 0, "desc": true}, "styles": [{"alias": "Time", "align": "auto", "dateFormat": "YYYY-MM-DD HH:mm:ss", "pattern": "Time", "type": "date"}, {"alias": "", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "decimals": 2, "pattern": "/.*/", "thresholds": [], "type": "number", "unit": "short"}], "targets": [{"expr": "ceph_osd_up{cluster=\"$cluster\"} == 0", "format": "table", "instant": true, "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Down OSD's", "transform": "table", "type": "table-old"}], "title": "<PERSON><PERSON><PERSON>", "type": "row"}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 12}, "id": 108, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 6, "x": 0, "y": 13}, "hiddenSeries": false, "id": 110, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.2.0", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "count by (ceph_version) (ceph_osd_metadata{cluster='$cluster'})", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{ ceph_version }}", "refId": "A", "target": ""}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Ceph OSD Versions", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": 0, "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 6, "x": 6, "y": 13}, "hiddenSeries": false, "id": 111, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.2.0", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "count by (ceph_version)(ceph_mon_metadata{cluster='$cluster'})", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{ ceph_version }}", "refId": "A", "target": ""}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "<PERSON><PERSON>s", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": 0, "format": "short", "label": "", "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 6, "x": 12, "y": 13}, "hiddenSeries": false, "id": 112, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.2.0", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "count by (ceph_version)(ceph_mds_metadata{cluster='$cluster'})", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{ ceph_version }}", "refId": "A", "target": ""}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Ceph MDS Versions", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": 0, "format": "short", "label": "", "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 6, "x": 18, "y": 13}, "hiddenSeries": false, "id": 113, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.2.0", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "count by (ceph_version)(ceph_rgw_metadata{cluster='$cluster'})", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{ ceph_version }}", "refId": "A", "target": ""}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Ceph RGW Versions", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": 0, "format": "short", "label": "", "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "title": "Ceph Versions", "type": "row"}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 13}, "id": 39, "panels": [], "repeat": null, "title": "CLUSTER", "type": "row"}, {"aliasColors": {"Available": "#EAB839", "Total Capacity": "#447EBC", "Used": "#BF1B00", "total_avail": "#6ED0E0", "total_space": "#7EB26D", "total_used": "#890F02"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "editable": true, "error": false, "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 4, "fillGradient": 0, "grid": {}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 14}, "height": "300", "hiddenSeries": false, "id": 1, "interval": "$interval", "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 0, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "Total Capacity", "fill": 0, "linewidth": 3, "stack": false}], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "sum without (instance) (ceph_cluster_total_bytes{cluster=\"$cluster\"})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Total Capacity", "refId": "C", "step": 300}, {"expr": "sum without (instance) (ceph_cluster_total_bytes{cluster=\"$cluster\"}-ceph_cluster_total_used_bytes{cluster=\"$cluster\"})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Available", "refId": "A", "step": 300}, {"expr": "sum without (instance) (ceph_cluster_total_used_bytes{cluster=\"$cluster\"})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Used", "refId": "B", "step": 300}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Capacity", "tooltip": {"msResolution": false, "shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:905", "format": "bytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:906", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {"Total Capacity": "#7EB26D", "Used": "#BF1B00", "total_avail": "#6ED0E0", "total_space": "#7EB26D", "total_used": "#890F02"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "decimals": 0, "editable": true, "error": false, "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 14}, "height": "300", "hiddenSeries": false, "id": 3, "interval": "$interval", "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "sum without (instance, ceph_daemon) (irate(ceph_osd_op_w{cluster=\"$cluster\"}[5m]))", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Write", "refId": "A", "step": 300}, {"expr": "sum without (instance, ceph_daemon) (irate(ceph_osd_op_r{cluster=\"$cluster\"}[5m]))", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Read", "refId": "B", "step": 300}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "IOPS", "tooltip": {"msResolution": true, "shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:2411", "format": "none", "label": "", "logBase": 1, "max": null, "min": 0, "show": true}, {"$$hashKey": "object:2412", "format": "short", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "editable": true, "error": false, "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 14}, "height": "300", "hiddenSeries": false, "id": 7, "interval": "$interval", "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "sum without (instance, ceph_daemon) (irate(ceph_osd_op_w_in_bytes{cluster=\"$cluster\"}[5m]))", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Write", "refId": "A", "step": 300}, {"expr": "sum without (instance, ceph_daemon) (irate(ceph_osd_op_r_out_bytes{cluster=\"$cluster\"}[5m]))", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Read", "refId": "B", "step": 300}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Throughput", "tooltip": {"msResolution": false, "shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:2382", "format": "decbytes", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"$$hashKey": "object:2383", "format": "short", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "description": "", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 22}, "hiddenSeries": false, "id": 78, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum((ceph_pool_num_bytes_recovered{cluster='$cluster'}) *on (instance, pool_id) group_left(name)(ceph_pool_metadata{cluster='$cluster'})) by (name)", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{name}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Pool Bytes Recovered", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:2148", "format": "bytes", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:2149", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "description": "", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 7, "x": 8, "y": 22}, "hiddenSeries": false, "id": 114, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum without (instance, pool_id) ((ceph_pool_num_objects_recovered{cluster='$cluster'}) *on (instance, pool_id) group_left(name)(ceph_pool_metadata{cluster='$cluster'}))", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{name}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Pool Objects Recovered", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:2148", "format": "short", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:2149", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 9, "x": 15, "y": 22}, "hiddenSeries": false, "id": 79, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "sum without (instance) ((ceph_pool_objects{cluster='$cluster'}) *on (instance, pool_id) group_left(name)(ceph_pool_metadata{cluster='$cluster'}))", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{name}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Objects Per Pool", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:795", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:796", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 30}, "hiddenSeries": false, "id": 80, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum without (instance) ((ceph_pool_quota_bytes{cluster='$cluster'}) *on (instance, pool_id) group_left(name)(ceph_pool_metadata{cluster='$cluster'}))", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{name}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Pool Quota Bytes", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:685", "format": "bytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:686", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 30}, "hiddenSeries": false, "id": 81, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum without (instance) (ceph_pool_quota_objects{cluster='$cluster'}) *on (instance, pool_id) group_left(name)(ceph_pool_metadata{cluster='$cluster'})", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{name}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Pool Objects Quota", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:656", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:657", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 30}, "hiddenSeries": false, "id": 106, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "count without (instance, ceph_daemon) (ceph_bluestore_commit_lat_count{cluster='$cluster'})", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "BlueStore", "refId": "A"}, {"expr": "count without (instance, ceph_daemon) (ceph_filestore_journal_latency_count{cluster='$cluster'})", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "FileStore", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "OSD Type Count", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 37}, "id": 41, "panels": [], "repeat": null, "title": "OBJECTS", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "editable": true, "error": false, "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 12, "w": 6, "x": 0, "y": 38}, "hiddenSeries": false, "id": 18, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "/^Total.*$/", "stack": false}], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "sum without (instance, pool_id) (ceph_pool_objects)", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Total", "refId": "A", "step": 300}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Objects in the Cluster", "tooltip": {"msResolution": false, "shared": true, "sort": 1, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "editable": true, "error": false, "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 12, "w": 8, "x": 6, "y": 38}, "hiddenSeries": false, "id": 19, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "/^Total.*$/", "stack": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum without (instance, pool_id) (ceph_pg_active{cluster=\"$cluster\"})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Active", "refId": "M"}, {"expr": "sum without (instance, pool_id) (ceph_pg_clean{cluster=\"$cluster\"})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Clean", "refId": "U"}, {"expr": "sum without (instance, pool_id) (ceph_pg_peering{cluster=\"$cluster\"})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Peering", "refId": "I"}, {"expr": "sum without (instance, pool_id) (ceph_pg_degraded{cluster=\"$cluster\"})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Degraded", "refId": "B", "step": 300}, {"expr": "sum without (instance, pool_id) (ceph_pg_stale{cluster=\"$cluster\"})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Stale", "refId": "C", "step": 300}, {"expr": "sum without (instance, pool_id) (ceph_unclean_pgs{cluster=\"$cluster\"})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "<PERSON><PERSON>", "refId": "D", "step": 300}, {"expr": "sum without (instance, pool_id) (ceph_pg_undersized{cluster=\"$cluster\"})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Undersized", "refId": "E", "step": 300}, {"expr": "sum without (instance, pool_id) (ceph_pg_incomplete{cluster=\"$cluster\"})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Incomplete", "refId": "G"}, {"expr": "sum without (instance, pool_id) (ceph_pg_forced_backfill{cluster=\"$cluster\"})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Forced Backfill", "refId": "H"}, {"expr": "sum without (instance, pool_id) (ceph_pg_inconsistent{cluster=\"$cluster\"})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Inconsistent", "refId": "F"}, {"expr": "sum without (instance, pool_id) (ceph_pg_forced_recovery{cluster=\"$cluster\"})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Forced Recovery", "refId": "J"}, {"expr": "sum without (instance, pool_id) (ceph_pg_creating{cluster=\"$cluster\"})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Creating", "refId": "K"}, {"expr": "sum without (instance, pool_id) (ceph_pg_wait_backfill{cluster=\"$cluster\"})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Wait Backfill", "refId": "L"}, {"expr": "sum without (instance, pool_id) (ceph_pg_deep{cluster=\"$cluster\"})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Deep", "refId": "N"}, {"expr": "sum without (instance, pool_id) (ceph_pg_scrubbing{cluster=\"$cluster\"})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Scrubbing", "refId": "O"}, {"expr": "sum without (instance, pool_id) (ceph_pg_recovering{cluster=\"$cluster\"})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Recovering", "refId": "P"}, {"expr": "sum without (instance, pool_id) (ceph_pg_repair{cluster=\"$cluster\"})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Repair", "refId": "Q"}, {"expr": "sum without (instance, pool_id) (ceph_pg_down{cluster=\"$cluster\"})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Down", "refId": "R"}, {"expr": "sum without (instance, pool_id) (ceph_pg_peered{cluster=\"$cluster\"})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Peered", "refId": "S"}, {"expr": "sum without (instance, pool_id) (ceph_pg_backfill{cluster=\"$cluster\"})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Backfill", "refId": "T"}, {"expr": "sum without (instance, pool_id) (ceph_pg_remapped{cluster=\"$cluster\"})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Remapped", "refId": "V"}, {"expr": "sum without (instance, pool_id) (ceph_pg_backfill_toofull{cluster=\"$cluster\"})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Backfill Toofull", "refId": "W"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "PGs State", "tooltip": {"msResolution": false, "shared": true, "sort": 1, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 2, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "editable": true, "error": false, "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 6, "w": 10, "x": 14, "y": 38}, "hiddenSeries": false, "id": 20, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "/^Total.*$/", "stack": false}], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "sum without (instance, pool_id) (ceph_pg_degraded{cluster=\"$cluster\"})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Degraded", "refId": "F", "step": 300}, {"expr": "sum without (instance, pool_id) (ceph_pg_stale{cluster=\"$cluster\"})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Stale", "refId": "A", "step": 300}, {"expr": "sum without (instance, pool_id) (ceph_pg_undersized{cluster=\"$cluster\"})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Undersized", "refId": "B", "step": 300}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Stuck PGs", "tooltip": {"msResolution": false, "shared": true, "sort": 1, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "editable": true, "error": false, "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 6, "w": 10, "x": 14, "y": 44}, "hiddenSeries": false, "id": 15, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum without (instance, ceph_daemon) (irate(ceph_osd_recovery_ops{cluster=\"$cluster\"}[$__rate_interval]))", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "OPS", "refId": "A", "step": 300}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Recovery Operations", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:2458", "format": "short", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"$$hashKey": "object:2459", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 50}, "id": 40, "panels": [], "repeat": null, "title": "LATENCY", "type": "row"}, {"cards": {"cardPadding": null, "cardRound": null}, "color": {"cardColor": "#b4ff00", "colorScale": "sqrt", "colorScheme": "interpolateOranges", "exponent": 0.5, "mode": "opacity"}, "dataFormat": "timeseries", "datasource": "Prometheus", "fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 51}, "heatmap": {}, "hideZeroBuckets": false, "highlightCards": true, "id": 83, "legend": {"show": true}, "links": [], "reverseYBuckets": false, "targets": [{"expr": "ceph_osd_apply_latency_ms{cluster='$cluster'}", "format": "time_series", "instant": false, "interval": "$interval", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "OSD Apply Latency Distribution", "tooltip": {"show": true, "showHistogram": false}, "type": "heatmap", "xAxis": {"show": true}, "xBucketNumber": null, "xBucketSize": "", "yAxis": {"decimals": null, "format": "ms", "logBase": 2, "max": null, "min": "0", "show": true, "splitFactor": 1}, "yBucketBound": "auto", "yBucketNumber": null, "yBucketSize": 10}, {"cards": {"cardPadding": null, "cardRound": null}, "color": {"cardColor": "#65c5db", "colorScale": "sqrt", "colorScheme": "interpolateOranges", "exponent": 0.5, "mode": "opacity"}, "dataFormat": "timeseries", "datasource": "Prometheus", "fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 51}, "heatmap": {}, "hideZeroBuckets": false, "highlightCards": true, "id": 84, "legend": {"show": true}, "links": [], "reverseYBuckets": false, "targets": [{"expr": "ceph_osd_commit_latency_ms{cluster='$cluster'}", "format": "time_series", "instant": false, "interval": "$interval", "intervalFactor": 1, "refId": "A"}], "title": "OSD Commit Latency Distribution", "tooltip": {"show": true, "showHistogram": false}, "type": "heatmap", "xAxis": {"show": true}, "xBucketNumber": null, "xBucketSize": "", "yAxis": {"decimals": null, "format": "ms", "logBase": 2, "max": null, "min": "0", "show": true, "splitFactor": null}, "yBucketBound": "auto", "yBucketNumber": null, "yBucketSize": null}, {"cards": {"cardPadding": null, "cardRound": null}, "color": {"cardColor": "#806eb7", "colorScale": "sqrt", "colorScheme": "interpolateOranges", "exponent": 0.5, "mode": "opacity"}, "dataFormat": "timeseries", "datasource": "Prometheus", "fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 59}, "heatmap": {}, "hideZeroBuckets": false, "highlightCards": true, "id": 85, "legend": {"show": true}, "links": [], "reverseYBuckets": false, "targets": [{"expr": "rate(ceph_osd_op_r_latency_sum{cluster=\"$cluster\"}[5m]) / rate(ceph_osd_op_r_latency_count{cluster=\"$cluster\"}[5m]) >= 0", "format": "time_series", "instant": false, "interval": "$interval", "intervalFactor": 1, "refId": "A"}], "title": "OSD Read Op Latency Distribution", "tooltip": {"show": true, "showHistogram": false}, "type": "heatmap", "xAxis": {"show": true}, "xBucketNumber": null, "xBucketSize": "", "yAxis": {"decimals": 2, "format": "ms", "logBase": 2, "max": null, "min": "0", "show": true, "splitFactor": null}, "yBucketBound": "auto", "yBucketNumber": null, "yBucketSize": null}, {"cards": {"cardPadding": null, "cardRound": null}, "color": {"cardColor": "#f9934e", "colorScale": "sqrt", "colorScheme": "interpolateOranges", "exponent": 0.5, "mode": "opacity"}, "dataFormat": "timeseries", "datasource": "Prometheus", "fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 59}, "heatmap": {}, "hideZeroBuckets": false, "highlightCards": true, "id": 86, "legend": {"show": true}, "links": [], "reverseYBuckets": false, "targets": [{"expr": "rate(ceph_osd_op_w_latency_sum{cluster=\"$cluster\"}[5m]) / rate(ceph_osd_op_w_latency_count{cluster=\"$cluster\"}[5m]) >= 0", "format": "time_series", "hide": false, "instant": false, "interval": "$interval", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "OSD Write Op Latency Distribution", "tooltip": {"show": true, "showHistogram": false}, "type": "heatmap", "xAxis": {"show": true}, "xBucketNumber": null, "xBucketSize": "", "yAxis": {"decimals": 2, "format": "ms", "logBase": 2, "max": null, "min": "0", "show": true, "splitFactor": null}, "yBucketBound": "auto", "yBucketNumber": null, "yBucketSize": null}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 67}, "hiddenSeries": false, "id": 44, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "avg without (instance,ceph_daemon) (rate(ceph_osd_op_r_latency_sum{cluster=\"$cluster\"}[5m]) / rate(ceph_osd_op_r_latency_count{cluster=\"$cluster\"}[5m]) >= 0)", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "read", "refId": "A"}, {"expr": "avg without (instance, ceph_daemon) (rate(ceph_osd_op_w_latency_sum{cluster=\"$cluster\"}[5m]) / rate(ceph_osd_op_w_latency_count{cluster=\"$cluster\"}[5m]) >= 0)", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "write", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Avg OSD  Op  Latency", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "decimals": null, "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 67}, "hiddenSeries": false, "id": 35, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "max": true, "min": false, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.2", "pointradius": 1, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "avg without (instance, ceph_daemon) (ceph_osd_apply_latency_ms{cluster=\"$cluster\"})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "apply", "metric": "ceph_osd_perf_apply_latency_seconds", "refId": "A", "step": 4}, {"expr": "avg without (instance, ceph_daemon) (ceph_osd_commit_latency_ms{cluster=\"$cluster\"})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "commit", "metric": "ceph_osd_perf_commit_latency_seconds", "refId": "B", "step": 4}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "AVG OSD Apply + Commit Latency", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1258", "decimals": null, "format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:1259", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 74}, "id": 61, "panels": [], "title": "Node Statistics (NodeExporter)", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 75}, "hiddenSeries": false, "id": 63, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": true, "min": true, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "node_memory_Active_anon_bytes{cluster=\"$cluster\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{instance}}", "refId": "A"}, {"expr": "sum without (instance) (node_memory_Active_anon_bytes{cluster='$cluster'})", "format": "time_series", "intervalFactor": 1, "legendFormat": "Cluster Memory Usage", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Node Memory Usage", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:2713", "format": "bytes", "label": null, "logBase": 2, "max": null, "min": null, "show": true}, {"$$hashKey": "object:2714", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 75}, "hiddenSeries": false, "id": 64, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "avg by (instance) (irate(node_cpu_seconds_total{cluster='$cluster',mode!=\"idle\"}[$interval])) * 100", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{instance}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Node CPU Usage", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:2684", "format": "percent", "label": null, "logBase": 2, "max": null, "min": null, "show": true}, {"$$hashKey": "object:2685", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 84}, "hiddenSeries": false, "id": 65, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum by (instance)(irate(node_disk_read_bytes_total{cluster='$cluster'}[$interval]))", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{instance}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Node Out", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:2742", "format": "decbytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:2743", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 84}, "hiddenSeries": false, "id": 66, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum by (instance)(irate(node_disk_written_bytes_total{cluster='$cluster'}[$interval]))", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{instance}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Node In", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:2771", "format": "decbytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:2772", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 93}, "hiddenSeries": false, "id": 68, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "(node_filesystem_free_bytes{cluster=\"$cluster\", mountpoint='/', device != 'rootfs'})*100 / (node_filesystem_size_bytes{cluster=\"$cluster\", mountpoint='/', device != 'rootfs'})", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{instance}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Free Space in root filesystem", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percent", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "refresh": "1m", "schemaVersion": 27, "style": "dark", "tags": ["ceph", "cluster"], "templating": {"list": [{"auto": true, "auto_count": 10, "auto_min": "1m", "current": {"selected": false, "text": "10s", "value": "10s"}, "datasource": null, "description": null, "error": null, "hide": 0, "includeAll": false, "label": "Interval", "multi": false, "name": "interval", "options": [{"selected": false, "text": "auto", "value": "$__auto_interval_interval"}, {"selected": false, "text": "5s", "value": "5s"}, {"selected": true, "text": "10s", "value": "10s"}, {"selected": false, "text": "30s", "value": "30s"}, {"selected": false, "text": "1m", "value": "1m"}, {"selected": false, "text": "10m", "value": "10m"}, {"selected": false, "text": "30m", "value": "30m"}, {"selected": false, "text": "1h", "value": "1h"}, {"selected": false, "text": "6h", "value": "6h"}, {"selected": false, "text": "12h", "value": "12h"}, {"selected": false, "text": "1d", "value": "1d"}, {"selected": false, "text": "7d", "value": "7d"}, {"selected": false, "text": "14d", "value": "14d"}, {"selected": false, "text": "30d", "value": "30d"}], "query": "5s,10s,30s,1m,10m,30m,1h,6h,12h,1d,7d,14d,30d", "queryValue": "", "refresh": 2, "skipUrlSync": false, "type": "interval"}, {"allValue": "cep<PERSON><PERSON><PERSON>|cep<PERSON>in|cephkelly", "current": {"isNone": true, "selected": false, "text": "None", "value": ""}, "datasource": "Prometheus", "definition": "", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "cluster", "multi": false, "name": "cluster", "options": [], "query": {"query": "label_values(cluster)", "refId": "Prometheus-cluster-Variable-Query"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-12h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "browser", "rows": [{}], "title": "Ceph - Cluster", "uid": "r6lloPJmz", "version": 0}, "overwrite": false}