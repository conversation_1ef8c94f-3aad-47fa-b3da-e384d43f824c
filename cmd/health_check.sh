#!/bin/bash

title_all=("ceph集群健康状态" "service服务状态" "node节点状态" "volume存储状态" "kolla容器健康状态" "超融合虚拟计算服务状态" "超融合虚拟卷服务状态
" "超融合虚拟网络服务状态" "虚拟机列表状态")

{
echo -e "本次巡检范围包括 "ceph集群健康状态"、"service服务状态"、"node节点状态"、"volume存储状态"、"kolla容器健康状态"、"超融合虚拟计算服务状态"、
"超融合虚拟卷服务状态"、"超融合虚拟网络服务状态"、"虚拟机列表状态"\n"
echo -e "已经开始运行巡检脚本。。。\n"
echo -e "即将为您展示为此次巡检的输出结果。。。\n"
sleep 3

source /root/cmd/openstack.sh
for title_1 in "${title_all[@]}"; do
    echo -e "================================================== $title_1 ==================================================\n"
    if [ $title_1 == "ceph集群健康状态" ];then
        ceph -s | grep "health" | awk '{$1=$1; print}'
        echo ""
    elif [ $title_1 == "service服务状态" ];then
        docker service ls
        echo ""
    elif [ $title_1 == "node节点状态" ];then
        docker node ls
        echo ""
    elif [ $title_1 == "volume存储状态" ];then
        docker volume ls
        echo ""
    elif [ $title_1 == "kolla容器健康状态" ];then
        docker ps -a|grep kolla
        echo ""
    elif [ $title_1 == "超融合虚拟计算服务状态" ];then
        openstack compute service list
        echo ""
    elif [ $title_1 == "超融合虚拟卷服务状态" ];then
        openstack volume service list
        echo ""
    elif [ $title_1 == "超融合虚拟网络服务状态" ];then
        openstack network agent list
        echo ""
    elif [ $title_1 == "虚拟机列表状态" ];then
        nova list
        echo ""
    fi
    echo -e "========================================================================================================================\n\n\n\n\n\n"
done

} | tee HCI-check-$(date +'%y%m%d').txt
echo -e "此次巡检输出结果已自动生成文件 HCI-check-$(date +'%y%m%d').txt\n"
