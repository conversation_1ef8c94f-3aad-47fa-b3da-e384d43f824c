#!/bin/bash
# 获取IP地址参数
ip=$1

# 获取所有运行中的虚拟机的名称
instances=$(docker exec nova_libvirt virsh list | awk 'NR>2 {print $2}')

# 遍历每个虚拟机
for instance in $instances; do
  # 获取虚拟机的IP地址
  instance_ip=$(docker exec nova_libvirt virsh dumpxml $instance | grep -oP '(?<=address=")[^"]*')

  # 如果虚拟机的IP地址与输入的IP地址匹配
  if [ "$instance_ip" == "$ip" ]; then
    # 获取虚拟机的graphics信息
    graphics=$(docker exec nova_libvirt virsh dumpxml $instance | grep -oP '(?<=<graphics).*(?=>)')

    # 提取listen和port信息
    port=$(echo $graphics | grep -o "port='[^']*'" | cut -d "'" -f 2)
    listen=$(echo $graphics | grep -o "listen='[^']*'" | cut -d "'" -f 2)
    port_output=$(echo "$port" | cut -d $'\n' -f 1)

    echo "       spice://$listen:$port_output"

    exit 0
  fi
done

echo "没有查询到对应ip,请检查: $ip 是否存在该节点上,是否启动,以及是否创建完成"
