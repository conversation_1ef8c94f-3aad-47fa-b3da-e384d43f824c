set timeout 10
set host_ip [ lindex $argv 0 ]
set vm_name [ lindex $argv 1 ]
set password [ lindex $argv 2 ]
#spawn ssh "root@$ip"
spawn virt-v2v-copy-to-local -ic esx://root@$host_ip?no_verify=1 $vm_name 
expect "Enter root's password for ***********:"
send "$password\r"
expect "Enter host password for user 'root':"
send "$password\r"
expect "Enter host password for user 'root':"
send "$password\r"
#expect "Enter host password for user 'root':"
#send "$password\r"
#expect "Enter host password for user 'root':"
#send "$password\r"
interact
#spawn export LIBGUESTFS_BACKEND=direct
#interact
spawn virt-v2v -i libvirtxml $vm_name.xml -of raw -o local -os .
interact
spawn find ./ -name "$vm_name-disk*" -type f  -print -exec rm -rf \{\} \\ \;
interact
