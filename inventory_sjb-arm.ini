[managers]
#arm
controller1 ansible_connection=ssh ansible_host=************** ansible_ssh_user=root ansible_ssh_pass="thecloud2015.1"
controller2 ansible_connection=ssh ansible_host=************** ansible_ssh_user=root ansible_ssh_pass="thecloud2015.1"
controller3 ansible_connection=ssh ansible_host=************** ansible_ssh_user=root ansible_ssh_pass="thecloud2015.1"
#x86
#controller1 ansible_connection=ssh ansible_host=*************** ansible_ssh_user=root ansible_ssh_pass="thecloud2015.1"
#controller2 ansible_connection=ssh ansible_host=*************** ansible_ssh_user=root ansible_ssh_pass="thecloud2015.1"
#controller3 ansible_connection=ssh ansible_host=*************** ansible_ssh_user=root ansible_ssh_pass="thecloud2015.1"

[workers]

[ceph-managers]
controller1 ansible_connection=ssh ansible_host=************** ansible_ssh_user=root ansible_ssh_pass="thecloud2015.1"
controller2 ansible_connection=ssh ansible_host=************** ansible_ssh_user=root ansible_ssh_pass="thecloud2015.1"
controller3 ansible_connection=ssh ansible_host=************** ansible_ssh_user=root ansible_ssh_pass="thecloud2015.1"

[admin]
client ansible_connection=ssh ansible_ssh_user=maojj ansible_host=*************

[docker-nodes:children]
managers
workers

[swarm-managers:children]
managers 

[swarm-workers:children]
workers

[docker:children]
managers
workers

[docker:vars]
ansible_python_interpreter=/usr/bin/python3
copy_ssh_enable=False
install_package=True
set_hostname=True
tianwen1="**************"

[deploy:children]
admin

[deploy:vars]
ansible_python_interpreter=/usr/bin/python3
copy_ssh_enable=False
install_package=False
set_hostname=False
tianwen1="**************"
