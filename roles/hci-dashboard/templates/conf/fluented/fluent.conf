<source>
  @type tail
  path "/fluentd/log/json*.log"
  pos_file "/fluentd/log/jsonlog.pos"
  tag "jsonlog"
  <parse>
    @type json
  </parse>
</source>

<filter jsonlog>
  @type record_transformer
  enable_ruby true
  <record>
    text ${record["description"]}
    date ${record["timestamp"]}
    system ${record["role"]}
    servername ${record["category"]}
    loglevel ${record["result"]}
    user ${record["username"]}
    clientip ${record["action"]}
    serverip ${"127.0.0.1"}
  </record>
</filter>

<match jsonlog>
  @type http
  endpoint "http://glc:8080/glc/v1/log/addBatch"
  headers {"X-GLC-AUTH":"glogcenter"}
  open_timeout 2
  http_method post
  json_array true
  <format>
    @type json
  </format>
  <buffer>
    flush_interval 5s
  </buffer>
</match>
