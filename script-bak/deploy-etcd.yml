---
- name: deploy theetcd
  hosts: managers
  vars:
    src_docker_compose: docker-compose.yml
    dest_docker_compose: /etc/thestack/compose/docker-compose-etcd.yml
    stack_name: theetcd
    is_remove: "{{ remove }}"
  tasks:

    - name: Print install_status
      debug:
        msg: "{{ is_remove }}"

    - name: install theetcd
      include_role:
        name: theetcd
      when:  is_remove == 'false'

    - name: uninstall theetcd
      shell: docker stack rm theetcd
      when: "is_remove == 'true'"
      run_once: true  
