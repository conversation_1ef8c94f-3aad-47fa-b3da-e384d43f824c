{"dashboard": {"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "description": "<PERSON><PERSON>s dashboard.", "editable": true, "gnetId": 5342, "graphTooltip": 0, "id": null, "iteration": 1687758904529, "links": [], "panels": [{"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 11, "panels": [], "title": "Pool: $pool", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "decimals": 2, "editable": true, "error": false, "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 3, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 20, "x": 0, "y": 1}, "height": "", "hiddenSeries": false, "id": 2, "interval": "$interval", "isNew": true, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.2", "pointradius": 0.5, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2955", "alias": "/^Total.*$/", "fill": 0, "linewidth": 4, "stack": false}, {"$$hashKey": "object:2956", "alias": "/^Raw.*$/", "color": "#BF1B00", "fill": 0, "linewidth": 4}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum((ceph_pool_max_avail) *on (pool_id) group_left(name)(ceph_pool_metadata{name=~\"^$pool$\"})) by (name)", "format": "time_series", "hide": false, "interval": "$interval", "intervalFactor": 1, "legendFormat": "Available - {{ name }}", "metric": "ceph_pool_available_bytes", "refId": "A", "step": 60}, {"expr": "sum((ceph_pool_stored) *on (pool_id) group_left(name)(ceph_pool_metadata{name=~\"^$pool$\"})) by (name)", "format": "time_series", "hide": false, "interval": "$interval", "intervalFactor": 1, "legendFormat": "Stored - {{ name }}", "metric": "ceph_pool", "refId": "B", "step": 60}, {"expr": "sum((ceph_pool_stored + ceph_pool_max_avail) *on (pool_id) group_left(name)(ceph_pool_metadata{name=~\"^$pool$\"})) by (name)", "format": "time_series", "hide": true, "interval": "$interval", "intervalFactor": 1, "legendFormat": "Total - {{ name }}", "metric": "ceph_pool", "refId": "C", "step": 60}, {"expr": "sum((ceph_pool_stored_raw) *on (pool_id) group_left(name)(ceph_pool_metadata{name=~\"^$pool$\"})) by (name)", "format": "time_series", "hide": false, "interval": "$interval", "intervalFactor": 1, "legendFormat": "Raw - {{ name }}", "metric": "ceph_pool", "refId": "D", "step": 60}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Pool Storage", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:2853", "format": "bytes", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"$$hashKey": "object:2854", "format": "short", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"cacheTimeout": null, "datasource": "Prometheus", "fieldConfig": {"defaults": {"decimals": 2, "mappings": [{"id": 0, "op": "=", "text": "N/A", "type": 1, "value": "null"}], "max": 1, "min": 0, "thresholds": {"mode": "percentage", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 75}, {"color": "red", "value": 90}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 7, "w": 4, "x": 20, "y": 1}, "id": 10, "interval": null, "links": [], "maxDataPoints": 100, "options": {"orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "text": {}}, "pluginVersion": "7.5.2", "targets": [{"expr": "sum without (instance, pool_id, name) ((ceph_pool_stored / (ceph_pool_stored + ceph_pool_max_avail)) *on (pool_id) group_left(name)(ceph_pool_metadata{name=~\"^$pool$\"}))", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "", "refId": "A", "step": 60}], "title": "Usage", "type": "gauge"}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 8}, "id": 12, "panels": [], "title": "Pool Info", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "editable": true, "error": false, "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 9}, "height": "", "hiddenSeries": false, "id": 7, "isNew": true, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum((ceph_pool_objects) *on (pool_id) group_left(name)(ceph_pool_metadata{name=~\"^$pool$\"})) by (name)", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Objects - {{ name }}", "refId": "A", "step": 60}, {"expr": "sum((ceph_pool_dirty) *on (pool_id) group_left(name)(ceph_pool_metadata{name=~\"^$pool$\"})) by (name)", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Dirty Objects - {{ name }}", "refId": "B", "step": 60}, {"expr": "sum((ceph_pool_quota_objects) *on (pool_id) group_left(name)(ceph_pool_metadata{name=~\"^$pool$\"})) by (name)", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Quota Objects - {{ name }}", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Objects in Pool", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "decimals": 2, "editable": true, "error": false, "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 9}, "hiddenSeries": false, "id": 4, "interval": "$interval", "isNew": true, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "sum((irate(ceph_pool_rd[3m])) *on (pool_id) group_left(name)(ceph_pool_metadata{name=~\"^$pool$\"})) by (name)", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Read - {{ name }}", "refId": "B", "step": 60}, {"expr": "sum((irate(ceph_pool_wr[3m])) *on (pool_id) group_left(name)(ceph_pool_metadata{name=~\"^$pool$\"})) by (name)", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Write - {{ name }}", "refId": "A", "step": 60}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "IOPS", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "none", "label": "IOPS", "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": "IOPS", "logBase": 1, "max": null, "min": 0, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "decimals": 2, "editable": true, "error": false, "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 17}, "hiddenSeries": false, "id": 5, "interval": "$interval", "isNew": true, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "sum((irate(ceph_pool_rd_bytes[5m])) *on (pool_id) group_left(name)(ceph_pool_metadata{name=~\"^$pool$\"})) by (name)", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Read Bytes - {{ name }}", "refId": "A", "step": 60}, {"expr": "sum((irate(ceph_pool_wr_bytes[5m])) *on (pool_id) group_left(name)(ceph_pool_metadata{name=~\"^$pool$\"})) by (name)", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Written Bytes - {{ name }}", "refId": "B", "step": 60}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Throughput", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "Bps", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "Bps", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "refresh": "1m", "schemaVersion": 27, "style": "dark", "tags": ["ceph", "pools"], "templating": {"list": [{"auto": true, "auto_count": 10, "auto_min": "1m", "current": {"selected": false, "text": "auto", "value": "$__auto_interval_interval"}, "datasource": null, "description": null, "error": null, "hide": 0, "includeAll": false, "label": "Interval", "multi": false, "name": "interval", "options": [{"selected": true, "text": "auto", "value": "$__auto_interval_interval"}, {"selected": false, "text": "10s", "value": "10s"}, {"selected": false, "text": "30s", "value": "30s"}, {"selected": false, "text": "1m", "value": "1m"}, {"selected": false, "text": "10m", "value": "10m"}, {"selected": false, "text": "30m", "value": "30m"}, {"selected": false, "text": "1h", "value": "1h"}, {"selected": false, "text": "6h", "value": "6h"}, {"selected": false, "text": "12h", "value": "12h"}, {"selected": false, "text": "1d", "value": "1d"}, {"selected": false, "text": "7d", "value": "7d"}, {"selected": false, "text": "14d", "value": "14d"}, {"selected": false, "text": "30d", "value": "30d"}], "query": "10s,30s,1m,10m,30m,1h,6h,12h,1d,7d,14d,30d", "queryValue": "", "refresh": 2, "skipUrlSync": false, "type": "interval"}, {"allValue": ".*", "current": {}, "datasource": "Prometheus", "definition": "label_values(ceph_pool_metadata, name)", "description": null, "error": {"data": {"message": "Unexpected error"}}, "hide": 0, "includeAll": true, "label": "Pool", "multi": true, "name": "pool", "options": [], "query": {"query": "label_values(ceph_pool_metadata, name)", "refId": "Prometheus-pool-Variable-Query"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 3, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-3h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "browser", "rows": [{}], "title": "Ceph - Pools", "uid": "-gtf0Bzik", "version": 0}, "overwrite": false}