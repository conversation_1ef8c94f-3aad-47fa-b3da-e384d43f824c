---
# - name: Create multiple directories
#   file:
#     path: "/etc/thestack/conf/fluentd"
#     state: directory
#     mode: '0755'


- name: deploy glc
  hosts: deploy
  vars:
    src_docker_compose: docker-compose.yml
    dest_docker_compose: /etc/thestack/compose/docker-compose-glc.yml

    src_fluentd_config: fluentd.conf
    dest_fluentd_config: /etc/thestack/conf/fluentd/fluentd.conf

    stack_name: glc
    is_remove: "{{ remove }}"
  tasks:

    - name: Print install_status
      debug:
        msg: "{{ is_remove }}"

    - name: install glc
      include_role:
        name: "../roles/glc"
      when:  is_remove == 'false'

    - name: uninstall glc
      shell: docker stack rm glc
      when: "is_remove == 'true'"
      run_once: true
