---
- name: deploy celery
  hosts: managers
  vars:
    src_docker_compose: docker-compose.yml
    dest_docker_compose: /etc/thestack/compose/docker-compose-celery.yml
    src_celery_config: data
    dest_celery_config: /etc/thestack/
    stack_name: celery
    is_remove: "{{ remove }}"
  tasks:

    - name: Print install_status
      debug:
        msg: "{{ is_remove }}"

    - name: install celery
      include_role:
        name: celery
      when:  is_remove == 'false'

    - name: uninstall celery
      shell: docker stack rm celery
      when: "is_remove == 'true'"
      run_once: true  
