version: "3.6"
services:
  reverse-proxy:
    # The official v2 Traefik docker image
    image: tianwen1:5000/traefik:v2.8
    # Enables the web UI and tells <PERSON><PERSON><PERSON><PERSON> to listen to docker
    command:
      - --log.level=DEBUG
      - --api.insecure=true
      - --providers.docker=true
      - --providers.docker.swarmMode=true
      - --providers.docker.exposedbydefault=false
      - --entryPoints.kibana.address=:5601
      - --entryPoints.grafana.address=:3000
      - --entryPoints.prometheus.address=:9090
      - --entryPoints.vdiserver.address=:8082
      - --entryPoints.mgr.address=:8886
      - --entryPoints.rabbitmq.address=:5672
      - --entryPoints.rabbitmq-mgmt.address=:15672
      - --entryPoints.mariadb.address=:3306
      #- --entryPoints.mariadb.address=**************:3306 
      - --entrypoints.ceph-api.address=:8445
      - --entryPoints.trigger.address=:9001
      - --entryPoints.recommend.address=:9002
      - --entryPoints.executor.address=:9003
      - --entryPoints.scheduler.address=:9004
      - --entryPoints.conductor.address=:9005
      - --entryPoints.elasticsearch.address=:9200
      - --entryPoints.canal.address=:11111
      - --entryPoints.web.address=:80
      - --entryPoints.web.http.redirections.entryPoint.to=websecure
      - --entryPoints.web.http.redirections.entryPoint.scheme=https
      - --entryPoints.websecure.address=:443
      #- --entryPoints.websecure.http.tls.certFile=/certs/yourdomain.crt
      #- --entryPoints.websecure.http.tls.keyFile=/certs/yourdomain.key
      - --providers.file.directory=/etc/traefik/dynamic
      - --providers.file.watch=true

    ports:
      # The HTTP port
      - "80:80"
      - "443:443"
      # The Web UI (enabled by --api.insecure=true)
      - "8080:8080"
      - "3306:3306"
      - "11111:11111"
      # - "5601:5601"
      - "8445:8445"
      - "8886:8886"
      - "3000:3000"
      - "9090:9090"
      - "8082:8082"
      - "9001:9001"
      - "9002:9002"
      - "9003:9003"
      - "9004:9004"
      - "9005:9005"
      # - "9200:9200"
      - "15672:15672"
      - "5672:5672"
    volumes:
      #- /etc/thestack/conf/traefik/yourdomain.crt:/certs/yourdomain.crt
      #- /etc/thestack/conf/traefik/yourdomain.key:/certs/yourdomain.key
      - /var/run/docker.sock:/var/run/docker.sock
      #- /etc/thestack/conf/traefik/certs-traefik.yml:/etc/traefik/dynamic/certs-traefik.yml
      #- /etc/traefik/dynamic:/etc/traefik/dynamic
    configs:
      - source: traefik_yourdomain_crt
        target: /certs/yourdomain.crt
      - source: traefik_yourdomain_key
        target: /certs/yourdomain.key
      - source: traefik_yourdomain_certs
        target: /etc/traefik/dynamic/certs-traefik.yml
      - source: traefik_tcp_routers
        target: /etc/traefik/dynamic/tcp_routers.yml

    deploy:
      mode: global
      placement:
        constraints:
          - node.role == manager
      labels:
        - "traefik.tcp.routers.mariadb.rule=HostSNI(`*`)"
        - "traefik.tcp.services.mariadb-service.loadbalancer.server.port=3306"
        - "traefik.tcp.routers.mgr.rule=HostSNI(`*`)"
        - "traefik.tcp.services.mgr-service.loadbalancer.server.port=8886"
        - "traefik.http.routers.api.rule=Host(`traefik.yourdomain.com`)"
        - "traefik.http.routers.api.service=api@internal" # Let the dashboard access the traefik api
    networks:
      prod:
        ipv4_address: **************


  acapi:
    image: tianwen1:5000/traefik-ac:latest
 #   ports:
 #     - 9998:9998
    networks:
      - prod
    deploy:
      replicas: 1
      labels:
        - traefik.enable=true
        - traefik.http.middlewares.acapi_strip.stripprefix.prefixes=/acapi
        - traefik.http.routers.acapi.rule=PathPrefix(`/acapi`)
        - traefik.http.routers.acapi.entrypoints=web,websecure
        - traefik.http.services.acapi.loadbalancer.server.port=9998
        - traefik.http.routers.acapi.middlewares=acapi_strip
        - traefik.http.routers.acapi.tls=true
        - traefik.docker.network=traefik_prod





configs:
  traefik_yourdomain_crt:
    file: /etc/thestack/conf/traefik/yourdomain.crt
  traefik_yourdomain_key:
    file: /etc/thestack/conf/traefik/yourdomain.key
  traefik_yourdomain_certs:
    file: /etc/thestack/conf/traefik/certs-traefik.yml
  traefik_tcp_routers:
    file: /etc/thestack/conf/traefik/tcp_routers.yml

networks:
  prod:
    name: prod
    driver: overlay
    attachable: true  # 添加这行
    # ipam:
    #   config:
    #     - subnet: "*************/24"  # 确保VIP在这个范围内
    driver_opts:
      com.docker.network.bridge.enable_icc: "true"
      com.docker.network.bridge.enable_ip_masquerade: "true"
