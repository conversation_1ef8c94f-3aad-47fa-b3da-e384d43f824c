version: "3.7"
services:
  celery_flower:
    image: tianwen1:5000/theasyn:latest
    hostname: celery_flower
    command: celery -A asynservices.tasks flower --address=0.0.0.0 --port=5555
    ports:
      - 5555:5555
    networks:
      - traefik_prod
    configs: 
      - source: celery_worker.py
        target: /code/settings.py
    volumes:
      - /var/log/the:/var/log/the
      - /usr/share/zoneinfo/Asia/Shanghai:/etc/localtime
    deploy:
      replicas: 1
      placement:
        constraints:
          - node.role==manager
    depends_on:
      - celery_beat
      - celery_worker

  celery_beat:
    image: tianwen1:5000/theasyn:latest
    hostname: celery_beat
    command: celery -A asynservices.tasks beat
    networks:
      - traefik_prod
    volumes:
      - /usr/share/zoneinfo/Asia/Shanghai:/etc/localtime
      - /var/log/the:/var/log/the
    configs: 
      - source: celery_beat.py
        target: /code/settings.py
    deploy:
      replicas: 1
      placement:
        constraints:
          - node.role==manager


  celery_worker:
    image: tianwen1:5000/theasyn:latest
    hostname: celery_worker
    command: celery -A asynservices.tasks worker --loglevel=INFO
    networks:
      - traefik_prod
    configs: 
      - source: celery_worker.py
        target: /code/settings.py
    volumes:
      - theshare:/data/share
      - /var/log/the:/var/log/the
      - /usr/share/zoneinfo/Asia/Shanghai:/etc/localtime
    deploy:
      replicas: 1
      placement:
        constraints:
          - node.role==manager
    depends_on:
      - celery_beat


  celery_migrate_beat:
    image: tianwen1:5000/themigrate:latest
    hostname: celery_migrate_beat
    command: celery -A services.tasks beat
    networks:
      - traefik_prod
    configs: 
      - source: celery_migrate_beat.py
        target: /code/settings.py
    volumes:
      - theshare:/data/share
      - /usr/share/zoneinfo/Asia/Shanghai:/etc/localtime
    deploy:
      replicas: 1
      placement:
        constraints:
          - node.role==manager



  celery_migrate:
    image: tianwen1:5000/themigrate:latest
    hostname: celery_migrate
    command: celery -A services.tasks worker --loglevel=INFO
    networks:
      - traefik_prod
    configs:
      - source: v2v_copy.sh
        target: /data/migrate/v2v_copy.sh
      - source: celery_migrate.py
        target: /code/settings.py
      - source: ceph.conf
        target: /code/ceph.conf
      - source: ceph.client.admin.keyring
        target: /code/ceph.client.admin.keyring
    volumes:
      - migrateshare:/data/migrate:rw
      - /usr/share/zoneinfo/Asia/Shanghai:/etc/localtime
      #- ./data/themigrate/v2v_copy.sh:/data/migrate/v2v_copy.sh
      #- ./data/theweb/settings.py:/code/settings.py
      #- ./data/theweb/ceph.conf:/code/ceph.conf
      #- ./data/theweb/ceph.client.admin.keyring:/code/ceph.client.admin.keyring
    deploy:
      replicas: 1
      placement:
        constraints:
          - node.role==manager
    depends_on:
      - celery_migrate_beat      

configs:
  v2v_copy.sh:
    file: /etc/thestack/data/themigrate/v2v_copy.sh
  # settings.py:
  #   file: /etc/thestack/data/theweb/settings.py
  ceph.conf:
    file: /etc/thestack/data/theweb/ceph.conf
  ceph.client.admin.keyring:
    file: /etc/thestack/data/theweb/ceph.client.admin.keyring
  celery_beat.py:
    file: /etc/thestack/conf/theweb/settings.py
  celery_worker.py:
    file: /etc/thestack/conf/theweb/settings.py
  celery_migrate_beat.py:
    file: /etc/thestack/conf/theweb/settings.py
  celery_migrate.py:
    file: /etc/thestack/conf/theweb/settings.py



networks:
  traefik_prod:
    external: true

volumes:
  theshare:
  migrateshare:
