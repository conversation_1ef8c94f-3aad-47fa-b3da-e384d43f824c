---
- name: Get CPU, memory, and disk information
  setup:

- name: Summarize CPU and memory information
  set_fact:
    total_cpus: "{{ ansible_processor_vcpus }}"
    total_memory: "{{ ansible_memtotal_mb }}"
  run_once: true

- name: Get Ceph storage information
  shell: "ceph -s"
  register: ceph_output
  delegate_to: "{{ groups['managers'][0] }}"
  run_once: true

- name: Parse Ceph available storage
  set_fact:
    ceph_available_storage: "{{ ceph_output.stdout | regex_search('([0-9.]+ TiB) avail') | regex_replace(' TiB avail', '') | int }}"
  run_once: true

- name: Display summarized information
  debug:
    msg:
      - "Total CPUs: {{ total_cpus }}"
      - "Total Memory: {{ total_memory }} MB"
      - "Ceph Available Storage: {{ ceph_available_storage }} TiB"
  run_once: true

- name: Check if the folder exists
  stat:
    path: /etc/kolla
  register: folder_stat
  when: "inventory_hostname == 'controller1'"

- name: Display folder existence status
  debug:
    msg: "{{ folder_stat.stat.exists }}" 
  when: "inventory_hostname == 'controller1'"

- name: mkdir -p /etc/thestack/compose
  shell: mkdir -p /etc/thestack/compose

- name: Upload config file
  copy:
    src: "{{ src_conf }}"
    dest: "{{ dest_conf }}"


- name: Check if destination file exists
  stat:
    path: "{{ dest_docker_compose }}"
  register: dest_docker_compose_file
  
- name: Upload docker compose file
  template:
    src: "{{ src_docker_compose }}"
    dest: "{{ dest_docker_compose }}"
  # when: dest_docker_compose_file.stat.exists == False

- name: delete prometheus stack
  shell: docker stack rm prometheus
  ignore_errors: true

- name: Delete OpenStack application credential
  command: docker exec -e OS_USERNAME=admin -e OS_PASSWORD={{ openstack_password }} -e OS_PROJECT_NAME=admin -e OS_USER_DOMAIN_NAME=Default -e OS_PROJECT_DOMAIN_NAME=Default -e OS_AUTH_URL=http://{{ vip }}:5000/v3 -e OS_IDENTITY_API_VERSION=3 -e LIBGUESTFS_BACKEND=direct nova_compute openstack application credential delete openstack_credential
  register: delete_credential_output
  when: "inventory_hostname == 'controller1' and folder_stat.stat.exists == True"
  ignore_errors: true

- name: Create OpenStack application credential
  command :  docker exec -e OS_USERNAME=admin -e OS_PASSWORD={{ openstack_password }} -e OS_PROJECT_NAME=admin -e OS_USER_DOMAIN_NAME=Default -e OS_PROJECT_DOMAIN_NAME=Default -e OS_AUTH_URL=http://{{ vip }}:5000/v3 -e OS_IDENTITY_API_VERSION=3 -e LIBGUESTFS_BACKEND=direct nova_compute openstack application credential create  openstack_credential --role admin --format json
  register: app_credential_output
  when: "inventory_hostname == 'controller1' and folder_stat.stat.exists == True"

- name: Set OpenStack Compute Quotas
  command :  docker exec -e OS_USERNAME=admin -e OS_PASSWORD={{ openstack_password }} -e OS_PROJECT_NAME=admin -e OS_USER_DOMAIN_NAME=Default -e OS_PROJECT_DOMAIN_NAME=Default -e OS_AUTH_URL=http://{{ vip }}:5000/v3 -e OS_IDENTITY_API_VERSION=3 -e LIBGUESTFS_BACKEND=direct nova_compute openstack quota set --instances 100 --ram {{ total_memory*3 }} --cores {{ total_cpus*3*4 }} admin
  when: "inventory_hostname == 'controller1' and folder_stat.stat.exists == True"

- name: Set OpenStack Volume  Quotas
  command :  docker exec -e OS_USERNAME=admin -e OS_PASSWORD={{ openstack_password }} -e OS_PROJECT_NAME=admin -e OS_USER_DOMAIN_NAME=Default -e OS_PROJECT_DOMAIN_NAME=Default -e OS_AUTH_URL=http://{{ vip }}:5000/v3 -e OS_IDENTITY_API_VERSION=3 -e LIBGUESTFS_BACKEND=direct nova_compute  openstack quota set --volumes 500  --snapshots 500 --gigabytes {{ ceph_available_storage }} admin
  when: "inventory_hostname == 'controller1' and folder_stat.stat.exists == True"

- name: Set OpenStack Network  Quotas
  command :  docker exec -e OS_USERNAME=admin -e OS_PASSWORD={{ openstack_password }} -e OS_PROJECT_NAME=admin -e OS_USER_DOMAIN_NAME=Default -e OS_PROJECT_DOMAIN_NAME=Default -e OS_AUTH_URL=http://{{ vip }}:5000/v3 -e OS_IDENTITY_API_VERSION=3 -e LIBGUESTFS_BACKEND=direct nova_compute openstack quota set --networks 50 --subnets 100 --ports 500 --routers 30 --floating-ips 50 --secgroups 100 --secgroup-rules 1000 admin
  when: "inventory_hostname == 'controller1' and folder_stat.stat.exists == True"

- name: Set facts for application credential
  set_fact:
    application_credential_id: "{{ (app_credential_output.stdout | from_json).id }}"
    user_id: "{{ (app_credential_output.stdout | from_json).user_id }}"
    application_credential_secret: "{{ (app_credential_output.stdout | from_json).secret }}"
  when: "inventory_hostname == 'controller1' and folder_stat.stat.exists == True "  


#######禁止更改prometheus配置文件 ,已注释
# - name: Check if destination file exists
#   stat:
#     path: "{{ dest_prometheus_conf }}"
#   register: dest_prometheus_conf_file

- name: upload prometheus conf
  template:
    src: "{{ src_prometheus_conf }}"
    dest: "{{ dest_prometheus_conf }}"
  when: "inventory_hostname == 'controller1' and folder_stat.stat.exists == True"  
  # when: "dest_prometheus_conf_file.stat.exists == False and inventory_hostname == 'controller1' and folder_stat.stat.exists == True"  

- name: upload prometheus conf
  template:
    src: "{{ src1_prometheus_conf }}"
    dest: "{{ dest1_prometheus_conf }}"
  when: "inventory_hostname == 'controller1' and folder_stat.stat.exists == False"  
  # when: "dest_prometheus_conf_file.stat.exists == False and inventory_hostname == 'controller1' and folder_stat.stat.exists == False"  

# - name: Deploy services
#   shell: docker stack deploy -c {{ dest_docker_compose }} {{ stack_name }}
#   run_once: true

# - name: Pause for 10 seconds to wait for services being running
#   pause:
#     seconds: 10
#   run_once: true
  
# - name: Ensure services deployed
#   shell: docker service ls
#   register: service_output

# - name: Output service list
#   debug:
#     msg: "{{ service_output.stdout_lines }}"

# - name: Show stacks
#   shell: docker stack list