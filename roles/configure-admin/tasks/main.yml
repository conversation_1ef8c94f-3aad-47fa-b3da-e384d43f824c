---
- name: Generate SSH keys
  shell: ssh-keygen -b 2048 -f /home/<USER>/.ssh/id_rsa -t rsa -q -N "" 
  args:
    creates: /home/<USER>/.ssh/id_rsa
- name: Copy Public Key Locally
  fetch:
    src: /home/<USER>/.ssh/id_rsa.pub
    dest: /etc/thestack/
    flat: yes
- name: Copy SSH Configuration
  copy:
    src: ssh_config
    dest: /home/<USER>/.ssh/config
    mode: 0644
- name: empty known_hosts
  file:
    state: absent
    path: ~/.ssh/known_hosts
- name: run ssh-keyscan to add keys to known_hosts
  shell: "ssh-keyscan {{ item }} >> ~/.ssh/known_hosts"
  with_items:
    - "swarm-manager"
    - "swarm-worker-1"
    - "swarm-worker-2"
