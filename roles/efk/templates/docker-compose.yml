version: "3.7"
services:
  elasticsearch:
    image: tianwen1:5000/elasticsearch-oss:v6.2.3curl
    hostname: elasticsearch
    #user: 1000
    environment:
      - SERVICE_NAME=elasticsearch
      - cluster.name=elasticsearch-cluster
      - bootstrap.memory_lock=false
      - "ES_JAVA_OPTS=-Xms1024m -Xmx1024m -XX:-AssumeMP"
    ulimits:
      nproc: 65535
      nofile:
        soft: 65536
        hard: 65536
      memlock:
        soft: -1
        hard: -1
    networks:
      - traefik_prod
    volumes:
      - theesdata:/usr/share/elasticsearch/data:rw
      - /usr/share/zoneinfo/Asia/Shanghai:/etc/localtime
    deploy:
      mode: replicated
      replicas: 1
      restart_policy:
        condition: any
        delay: 10s
        window: 120s
      resources:
        limits:
          memory: 2g
      placement:
        constraints:
          - node.role==manager
      labels:
        - traefik.enable=true
        - traefik.docker.network=traefik_prod
        - traefik.tcp.routers.elasticsearch.rule=HostSNI(`*`)
        - traefik.tcp.routers.elasticsearch.entrypoints=elasticsearch
        - traefik.tcp.routers.elasticsearch.tls=false
        - traefik.tcp.routers.elasticsearch.service=elasticsearch
        - traefik.tcp.services.elasticsearch.loadbalancer.server.port=9200

  fluentd:
    image: tianwen1:5000/thxh-fluentd
    hostname: fluentd
    networks:
      - traefik_prod
    volumes:
      #- /mnt/ceph/data/etc:/fluentd/etc
      - /var/log/the:/fluentd/log
      - /usr/share/zoneinfo/Asia/Shanghai:/etc/localtime
    configs:
      - source: fluent.conf
        target: /fluentd/etc/fluent.conf
    deploy:
      mode: global
      restart_policy:
        condition: any
        delay: 10s
        window: 180s
      placement:
        constraints:
          - node.role==manager

  kibana:
    image: tianwen1:5000/kibana/kibana-oss:6.2.3
    hostname: kibana
    environment:
      SERVER_NAME: kibana-server
      ELASTICSEARCH_URL: http://elasticsearch:9200
    volumes:
      - /usr/share/zoneinfo/Asia/Shanghai:/etc/localtime
    #ports:
    #  - 5601:5601
    depends_on:
      - elasticsearch
    networks:
      - traefik_prod
    deploy:
      mode: replicated
      replicas: 1
      placement:
        constraints:
          - node.role == manager
      restart_policy:
        condition: any
        delay: 10s
        window: 180s
      labels:
        - traefik.enable=true
        - traefik.docker.network=traefik_prod
        - traefik.tcp.routers.kibana.rule=HostSNI(`*`)
        - traefik.tcp.routers.kibana.entrypoints=kibana
        - traefik.tcp.routers.kibana.tls=false
        - traefik.tcp.routers.kibana.service=kibana
        - traefik.tcp.services.kibana.loadbalancer.server.port=5601
    healthcheck:
      #curl -s -w "%{http_code}\n" -o /dev/null  http://kibana:5601/app/kibana
      test: ["CMD-SHELL", "curl -sf -o /dev/null 'http://kibana:5601/app/kibana' || exit 1"]
      interval: 1m
      timeout: 10s
      retries: 5

configs:
  fluent.conf:
    file: /etc/thestack/conf/efk/fluent.conf

volumes:
  theesdata:
    external: true

networks:
  traefik_prod:
    external: true
