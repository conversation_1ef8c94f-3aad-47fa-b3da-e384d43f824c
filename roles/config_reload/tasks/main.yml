---

- name: mkdir -p /etc/thestack/compose
  shell: mkdir -p /etc/thestack/compose

- name: Upload config file
  copy:
    src: "{{ src_conf }}"
    dest: "{{ dest_conf }}"

- name: Upload docker compose file
  template:
    src: "{{ src_docker_compose }}"
    dest: "{{ dest_docker_compose }}"

- name: upload prometheus conf
  template:
    src: "{{ src_prometheus_conf }}"
    dest: "{{ dest_prometheus_conf }}"

- name: upload traefik router conf
  template:
    src: "{{ src_traefik_routter_conf }}"
    dest: "{{ dest_traefik_routter_conf }}"

- name: upload haproxy conf
  template:
    src: "{{ src_haproxy_conf }}"
    dest: "{{ dest_haproxy_conf }}"

- name: upload theweb conf
  template:
    src: "{{ src_theweb_conf }}"
    dest: "{{ dest_theweb_conf }}"

- name: upload keeplived conf
  template:
    src: "{{ src_keeplived_conf }}"
    dest: "{{ dest_keeplived_conf }}"


- name: Load compose.yml from remote host
  slurp:
    src: /etc/thestack/compose/docker-compose-conf.yml
  register: config_data_slurp

- name: Set config_data variable
  set_fact:
    config_data: "{{ config_data_slurp['content'] | b64decode | from_yaml }}"

- name: Deploy services
  shell: /root/updateconfig.sh theconf_{{ item.key }} {{ item.value.file }}
  with_dict: "{{ config_data.configs }}"
  run_once: true

- name: Pause for 10 seconds to wait for services being running
  pause:
    seconds: 10
  run_once: true
  
- name: Ensure services deployed
  shell: docker service ls
  register: service_output

- name: Output service list
  debug:
    msg: "{{ service_output.stdout_lines }}"

- name: Show stacks
  shell: docker stack list