#!/bin/bash
galera_list=(
galera1
galera2
galera3
)

controller_list=(
controller1
controller2
controller3
)

check_galera(){
        echo '======================================galera集群====================================='
        for host in ${galera_list[@]}
        do
        state=`ssh $host "systemctl status mariadb"|grep Active|awk '{print $3}'`
        echo $host 'mariadb'  $state
        echo '-------------------------------------------------------------------------------------'
        done
        echo -e '=====================================================================================\n'
}

check_rabbitmq(){
        echo '=====================================rabbitmq服务===================================='
        for mq in ${controller_list[@]}
        do
        state=`ssh $mq "systemctl status rabbitmq-server"|grep Active|awk '{print $3}'`
        echo $mq 'rabbitmq'  $state
        echo '-------------------------------------------------------------------------------------'
        done
        echo -e '=====================================================================================\n'
}
check_ceph(){
        health=`ceph health|awk '{print $1}'`
        echo '====================================ceph集群健康状态================================='
        echo $health
        echo -e '=====================================================================================\n'
}

check_nova(){
        echo -e '========================================nova服务=====================================\n'
        openstack compute service list
        echo ''
}

check_cinder(){
        echo -e '=======================================cinder服务====================================\n'
        openstack volume service list
        echo ''
}

check_neutron(){
        echo -e '=======================================Neutron服务====================================\n'
        openstack network agent list
        echo ''
}
echo $(date)
#check_galera
#check_rabbitmq
check_ceph
check_nova
check_cinder
check_neutron
