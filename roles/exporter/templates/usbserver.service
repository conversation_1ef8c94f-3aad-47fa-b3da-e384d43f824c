[Unit]
Description=usbserver
After=network-online.target local-fs.target time-sync.target
Wants=network-online.target local-fs.target time-sync.target

[Service]
LimitNOFILE=1048576
LimitNPROC=1048576
EnvironmentFile=-/etc/environment
ExecStartPre=-/usr/bin/docker rm -f usbserver
ExecStart=-/usr/bin/docker run --name usbserver --net=host --privileged -v /etc/thestack/conf/usbserver/config.ini:/etc/usbserver/config.ini tianwen1:5000/theusbserver:latest
ExecStop=-/usr/bin/docker stop usbserver
KillMode=none
Restart=on-failure
RestartSec=10s
TimeoutStartSec=120
TimeoutStopSec=120
StartLimitInterval=30min
StartLimitBurst=5

[Install]
WantedBy=default.target
