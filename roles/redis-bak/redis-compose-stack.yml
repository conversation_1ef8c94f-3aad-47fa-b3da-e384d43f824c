version: '3.4'

services:
  redis-master:
    image: 'tianwen1:5000/bitnami/redis:5.0.2'
    hostname: redis
    ports:
      - '6379:6379'
    networks:
      - redis
    environment:
      - REDIS_REPLICATION_MODE=master 
      - REDIS_PASSWORD=f7paul12-d571-4701-9c55-64<PERSON><PERSON>cDyK
      - ALLOW_EMPTY_PASSWORD=yes
    deploy:
      mode: global
      restart_policy:
        condition: any
    volumes:
      - './redis:/opt/bitnami/redis/etc/redis'

  redis-replica:
    image: 'tianwen1:5000/bitnami/redis:5.0.2'
    hostname: master-node
    ports:
      - '6379'
    networks:
      - redis
    depends_on:
      - redis-master
    environment:
      - REDIS_REPLICATION_MODE=slave
      - REDIS_MASTER_HOST=redis-master
      - REDIS_MASTER_PORT_NUMBER=6379
      - REDIS_MASTER_PASSWORD=f7paul12-d571-4701-9c55-64vanacecDyK
      - REDIS_PASSWORD=f7paul12-d571-4701-9c55-64<PERSON><PERSON><PERSON><PERSON><PERSON>K
    deploy:
      mode: replicated
      replicas: 6
      update_config:
        parallelism: 1
        delay: 20s
      restart_policy:
        condition: any

  redis-sentinel:
    image: 'tianwen1:5000/bitnami/redis:5.0.2'
    ports:
      - '16379'
    networks:
      - redis
    depends_on:
      - redis-master
      - redis-replica
    environment:
      - ALLOW_EMPTY_PASSWORD=yes
      - REDIS_MASTER_HOST=redis-master

    entrypoint: |
      bash -c 'bash -s <<EOF
      "/bin/bash" -c "cat <<EOF > /opt/bitnami/redis/etc/sentinel.conf
      port 16379
      dir /etc/thestack/
      sentinel monitor master-node redis-master 6379 2
      sentinel down-after-milliseconds master-node 5000
      sentinel parallel-syncs master-node 1
      sentinel failover-timeout master-node 5000
      sentinel auth-pass master-node f7paul12-d571-4701-9c55-64vanacecDyK
      sentinel announce-ip redis-sentinel
      sentinel announce-port 16379
      EOF"     
      "/bin/bash" -c "redis-sentinel /opt/bitnami/redis/etc/sentinel.conf"    
      EOF'
    deploy:
      mode: global
      restart_policy:
        condition: any

networks:
  redis:
    driver: overlay
            

