---

#
#删除节点
#

#降级节点
- name: demote node in Docker swarm cluster 
  shell: 'docker node demote {{ delete_nodes }}' 
  ignore_errors: true
  when: "inventory_hostname == groups['managers'][0]"

- name: Pause for 10 seconds to wait for services being running
  pause:
    seconds: 10
  run_once: true

#删除节点
- name: delete node in Docker swarm cluster 
  shell: 'docker node rm  {{ delete_nodes }}' 
  ignore_errors: true
  when: "inventory_hostname == groups['managers'][0]"

- name: Pause for 10 seconds to wait for services being running
  pause:
    seconds: 10
  run_once: true

#待删除节点主动脱离集群
- name:  node leave the Docker swarm cluster 
  shell: 'docker swarm leave --force' 
  ignore_errors: true
  when: "inventory_hostname == delete_nodes "

- name: Pause for 20 seconds to wait for services being running
  pause:
    seconds: 20
  run_once: true

#删除节点
- name: delete node in Docker swarm cluster again
  shell: 'docker node rm {{ delete_nodes }}' 
  ignore_errors: true
  when: "inventory_hostname == groups['managers'][0]"