[Unit]
Description=HCI Local Asyn Agent Service
After=network-online.target local-fs.target time-sync.target
Wants=network-online.target local-fs.target time-sync.target

[Service]
Type=simple
User=root
Group=root
WorkingDirectory={{ base_dir }}/hci_asyn
Environment=PYTHONPATH={{ base_dir }}/python_packages:{{ base_dir }}/hci_api:{{ base_dir }}/hci_db
ExecStart=/usr/bin/python3 agent_runner.py agent --queue queue_{{ ansible_host }}
StandardOutput=append:{{ base_dir }}/hci_asyn/agent.log
StandardError=append:{{ base_dir }}/hci_asyn/agent.log
Restart=on-failure
RestartSec=10s
TimeoutStartSec=120
TimeoutStopSec=120
StartLimitInterval=30min
StartLimitBurst=5
LimitNOFILE=1048576
LimitNPROC=1048576

[Install]
WantedBy=multi-user.target
