---
- name: deploy efk
  hosts: managers
  vars:
    src_docker_compose: docker-compose.yml
    dest_docker_compose: /etc/thestack/compose/docker-compose-efk.yml
    stack_name: efk
    is_remove: "{{ remove }}"
  tasks:
    - name: Print install_status
      debug:
        msg: "{{ is_remove }}"

    - name: install efk
      include_role:
        name: efk
      when:  is_remove == 'false'

    - name: uninstall efk
      shell: docker stack rm efk
      when: "is_remove == 'true'"
      run_once: true  