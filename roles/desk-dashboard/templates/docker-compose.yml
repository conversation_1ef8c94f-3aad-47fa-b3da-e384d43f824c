version: "3.7"
services:
  thedeskvue:
    image: tianwen1:5000/thedeskvue:latest
    networks:
      - traefik_prod
    deploy:
      replicas: 2
      labels:
        - traefik.enable=true
        - traefik.http.middlewares.thedeskvue_strip.stripprefix.prefixes=/thedesk
        - traefik.http.routers.thedeskvue.rule=PathPrefix(`/thedesk`)
        - traefik.http.routers.thedeskvue.entrypoints=web,websecure
        - traefik.http.services.thedeskvue.loadbalancer.server.port=8067
        - traefik.http.routers.thedeskvue.middlewares=thedeskvue_strip
        - traefik.http.routers.thedeskvue.tls=true
        - traefik.docker.network=traefik_prod

  thedesk:
    image: tianwen1:5000/thedesk:latest
    hostname: thedesk
    # ports:
    #   - 8085:8085
    networks:
      - traefik_prod
    volumes:
      - /var/log/the:/var/log/the
      - /usr/share/zoneinfo/Asia/Shanghai:/etc/localtime
    configs:
      - source: thedesk.py
        target: /code/settings.py
    deploy:
      replicas: 1
      labels:
        - traefik.enable=true
        - traefik.http.middlewares.thedesk_strip.stripprefix.prefixes=/thedeskapi
        - traefik.http.routers.thedesk.rule=PathPrefix(`/thedeskapi`)
        - traefik.http.routers.thedesk.entrypoints=web,websecure
        - traefik.http.services.thedesk.loadbalancer.server.port=8085
        - traefik.http.routers.thedesk.middlewares=thedesk_strip
        - traefik.http.routers.thedesk.tls=true
        - traefik.docker.network=traefik_prod

  goapi:
    image: tianwen1:5000/thedeskapi:latest
    hostname: goapi
    # ports:
    #   - 8086:8086
    configs:
      - source: thedeskapi.ini
        target: /opt/thedesk/conf/app.ini
    networks:
      - traefik_prod
    volumes:
      - /var/log/the:/var/log/the
      - /usr/share/zoneinfo/Asia/Shanghai:/etc/localtime
      - /var/run/docker.sock:/var/run/docker.sock
    deploy:
      replicas: 1
      labels:
        - traefik.enable=true
        - "traefik.http.middlewares.goapi_strip.stripprefix.prefixes=/thedesknewapi"
        - traefik.http.routers.goapi.rule=PathPrefix(`/thedesknewapi`)
        - traefik.http.routers.goapi.entrypoints=web,websecure
        - traefik.http.services.goapi.loadbalancer.server.port=8086
        - traefik.http.routers.goapi.middlewares=goapi_strip
        - traefik.http.routers.goapi.tls=true
        - traefik.docker.network=traefik_prod



  celery_desk_beat:
    image: tianwen1:5000/theasyn:latest
    hostname: celery_desk_beat
    command: celery -A asynservices.cron beat
    networks:
      - traefik_prod
    volumes:
      - /usr/share/zoneinfo/Asia/Shanghai:/etc/localtime
      - /var/log/the:/var/log/the
    configs: 
      - source: thedesk.py
        target: /code/settings.py
    deploy:
      replicas: 1
      placement:
        constraints:
          - node.role==manager


  celery_desk_worker:
    image: tianwen1:5000/theasyn:latest
    hostname: celery_desk_worker
    command: celery -A asynservices.cron worker --loglevel=INFO
    networks:
      - traefik_prod
    configs: 
      - source: thedesk.py
        target: /code/settings.py
    volumes:
      - theshare:/data/share
      - /var/log/the:/var/log/the
      - /usr/share/zoneinfo/Asia/Shanghai:/etc/localtime
    deploy:
      replicas: 1
      placement:
        constraints:
          - node.role==manager
    depends_on:
      - celery_desk_beat
      
              
  vdiserver:
    image: tianwen1:5000/vdiserver:latest
    hostname: vdiserver
    networks:
      - traefik_prod
    # ports:
    #   - 8082:8082
    volumes:
      - /var/log/the:/var/log/the
      - /usr/share/zoneinfo/Asia/Shanghai:/etc/localtime
    configs:
      - source: thedeskapi.ini
        target: /opt/vdiserver/conf/app.ini
    deploy:
      replicas: 1
      labels:
        - traefik.enable=true
        - traefik.docker.network=traefik_prod
        - traefik.tcp.routers.vdiserver.rule=HostSNI(`*`)
        - traefik.tcp.routers.vdiserver.entrypoints=vdiserver
        - traefik.tcp.routers.vdiserver.tls=false
        - traefik.tcp.routers.vdiserver.service=vdiserver
        - traefik.tcp.services.vdiserver.loadbalancer.server.port=8082
        
  vdiws:
    image: tianwen1:5000/thechannel:latest
    networks:
      - traefik_prod
    command: python3 -u server.py
    configs:
      - source: thedesk.py
        target: /code/settings.py
    # ports:
    #   - 9901:9001
    deploy:
      replicas: 1
      labels:
        - traefik.enable=true
        - traefik.http.middlewares.vdiws_strip.stripprefix.prefixes=/vdiws
        - traefik.http.routers.vdiws.rule=PathPrefix(`/vdiws`)
        - traefik.http.routers.vdiws.entrypoints=web,websecure
        - traefik.http.services.vdiws.loadbalancer.server.port=9901
        - traefik.http.routers.vdiws.middlewares=vdiws_strip
        - traefik.http.routers.vdiws.tls=true
        - traefik.docker.network=traefik_prod
        
  vdiworker:
    image: tianwen1:5000/thechannel:latest
    networks:
      - traefik_prod
    command: python3 -u worker.py
    configs:
      - source: thedesk.py
        target: /code/settings.py

volumes:
  theshare:
  
networks:
  traefik_prod:
    external: true

configs:
  thedesk.py:
    file: /etc/thestack/conf/thedesk/settings.py
  thedeskapi.ini:
    file: /etc/thestack/conf/thedesk/config.ini
