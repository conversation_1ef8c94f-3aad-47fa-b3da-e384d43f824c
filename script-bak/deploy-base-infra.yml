- import_playbook: deploy-service-del.yml
- import_playbook: deploy-cmd.yml
- import_playbook: deploy-conf.yml
- import_playbook: deploy-keeplived.yml
- import_playbook: deploy-haproxy.yml
- import_playbook: deploy-traefik.yml
- import_playbook: deploy-healthcheck.yml
- import_playbook: deploy-docker-plugin.yml
- import_playbook: deploy-mariadb.yml
- import_playbook: deploy-etcd.yml
- import_playbook: deploy-thedb-bootstrap.yml
- import_playbook: deploy-exporter.yml
- import_playbook: deploy-rabbitmq.yml
- import_playbook: deploy-redis.yml
- import_playbook: deploy-kolla.yml


- name: Import deploy-kingbase if kingbase is true 
  import_playbook: deploy-kingbase.yml 
  when: kingbase is defined and kingbase
  