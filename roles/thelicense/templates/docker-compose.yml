version: "3.7"
services:
  thelicense:
    hostname: thelicense
    image: tianwen1:5000/thelicense:latest
    configs:
      - source: thelicense.ini
        target: /opt/thelicense/conf/app.ini
    networks:
      - hostnet
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - /usr/share/zoneinfo/Asia/Shanghai:/etc/localtime
      - /etc/hostname:/etc/host_name
    networks:
      - traefik_prod
    deploy:
      mode: replicated
      replicas: 1
      restart_policy:
        condition: any
        delay: 10s
        window: 120s
      placement:
        constraints:
          - node.role==manager
      labels:
        - traefik.enable=true
        - traefik.http.middlewares.thelicense_strip.stripprefix.prefixes=/thelicense
        - traefik.http.routers.thelicense.rule=PathPrefix(`/thelicense`)
        - traefik.http.routers.thelicense.entrypoints=web
        - traefik.http.services.thelicense.loadbalancer.server.port=8059
        - traefik.http.routers.thelicense.middlewares=thelicense_strip
        - traefik.docker.network=traefik_prod


networks:
  traefik_prod:
    external: true

configs:
  thelicense.ini:
    file: /etc/thestack/conf/thelicense/thelicense.ini