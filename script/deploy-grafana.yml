---
- name: deploy grafana
  hosts: deploy
  vars:
    src_docker_compose: docker-compose.yml
    dest_docker_compose: /etc/thestack/compose/docker-compose-grafana.yml
    stack_name: grafana
    is_remove: "{{ remove }}"
  tasks:

    - name: Print install_status
      debug:
        msg: "{{ is_remove }}"

    - name: install grafana
      include_role:
        name: "../roles/grafana"
      when:  is_remove == 'false'

    - name: uninstall grafana
      shell: docker stack rm grafana
      when: "is_remove == 'true'"
      run_once: true  
