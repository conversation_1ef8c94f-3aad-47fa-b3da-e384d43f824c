---
- name: Get the worker join-token
  shell: docker swarm join-token --quiet worker
  register: worker_token
  tags: swarm
  changed_when: false
  delegate_to: "{{ groups['managers'][0] }}"
  delegate_facts: true


- name: Add Workers to the Swarm 
  shell:  "docker swarm join --token {{ worker_token.stdout  }} {{ hostvars['controller1']['ansible_host'] }}:2377"
  tags: swarm

#- name: Add Workers to the Swarm
#  docker_swarm:
#    state: join
#    advertise_addr: "{{ ansible_host }}"
#    join_token: "{{ hostvars['controller1']['worker_token']['stdout'] }}"
#    remote_addrs: [ "{{ hostvars['controller1']['ansible_default_ipv4']['address'] }}:2377" ]
#  tags: swarm
