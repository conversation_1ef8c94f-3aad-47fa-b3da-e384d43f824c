#!/bin/bash
publish_port(){
docker service update --publish-add published='{ print $3 }',target='{ print $2 }' '{ print $1 }'
}


echo -e "==========================================\n"
echo "输入服务名："
echo -e "==========================================\n"
read -p "请输入：" service_name

echo -e "==========================================\n"
echo "源端口："
echo -e "==========================================\n"
read -p "请输入：" target

echo -e "==========================================\n"
echo "目标端口："
echo -e "==========================================\n"
read -p "请输入：" published

publish_port
echo "服务端口已映射"
echo -e "------------------------------------------\n"
