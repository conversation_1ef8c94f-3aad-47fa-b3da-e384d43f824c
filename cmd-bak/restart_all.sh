docker ps -a | grep $name | awk '{ print $1 }'  | xargs docker restart


docker stack deploy -c /etc/thestack/compose/docker-compose-celery.yml celery

docker stack deploy -c /etc/thestack/compose/docker-compose-dayu.yml dayu

docker stack deploy -c /etc/thestack/compose/docker-compose-dashboard.yml dashboard

docker stack deploy -c /etc/thestack/compose/docker-compose-desk-dashboard.yml desk-dashboard

