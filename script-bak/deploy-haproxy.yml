---
- name: deploy haproxy
  hosts: managers
  vars:
    src_docker_compose: docker-compose.yml
    dest_docker_compose: /etc/thestack/compose/docker-compose-haproxy.yml
    stack_name: haproxy
    is_remove: "{{ remove }}"
  tasks:

    - name: Print install_status
      debug:
        msg: "{{ is_remove }}"

    - name: install haproxy
      include_role:
        name: haproxy
      when:  is_remove == 'false'

    - name: uninstall haproxy
      shell: docker stack rm haproxy
      when: "is_remove == 'true'"
      run_once: true  
