---
- name: Upload docker compose file
  template:
    src: "{{ src_docker_compose }}"
    dest: "{{ dest_docker_compose }}"

- name: Check if destination conductor file exists
  stat:
    path: "{{ dest_conductor_conf }}"
  register: dest_conductor_conf_file

- name: upload conductor conf
  template:
    src: "{{ src_conductor_conf }}"
    dest: "{{ dest_conductor_conf }}"
  when: dest_conductor_conf_file.stat.exists == False


- name: Check if destination dest_scheduler_conf file exists
  stat:
    path: "{{ dest_scheduler_conf }}"
  register: dest_scheduler_conf_file

- name: upload scheduler conf
  template:
    src: "{{ src_scheduler_conf }}"
    dest: "{{ dest_scheduler_conf }}"
  when: dest_scheduler_conf_file.stat.exists == False


- name: Check if destination dest_trigger_conf file exists
  stat:
    path: "{{ dest_trigger_conf }}"
  register: dest_trigger_conf_file

- name: upload trigger conf
  template:
    src: "{{ src_trigger_conf }}"
    dest: "{{ dest_trigger_conf }}"
  when: dest_trigger_conf_file.stat.exists == False


- name: Check if destination dest_executor_conf file exists
  stat:
    path: "{{ dest_executor_conf }}"
  register: dest_executor_conf_file

- name: upload executor conf
  template:
    src: "{{ src_executor_conf }}"
    dest: "{{ dest_executor_conf }}"
  when: dest_executor_conf_file.stat.exists == False


- name: Check if destination dest_recommend_conf file exists
  stat:
    path: "{{ dest_recommend_conf }}"
  register: dest_recommend_conf_file

- name: upload recommend conf
  template:
    src: "{{ src_recommend_conf }}"
    dest: "{{ dest_recommend_conf }}"
  when: dest_recommend_conf_file.stat.exists == False


- name: Deploy services
  shell: docker stack deploy -c {{ dest_docker_compose }} {{ stack_name }}
  run_once: true

- name: Pause for 10 seconds to wait for services being running
  pause:
    seconds: 10
  run_once: true
  
- name: Ensure services deployed
  shell: docker service ls
  register: service_output

- name: Output service list
  debug:
    msg: "{{ service_output.stdout_lines }}"

- name: Show stacks
  shell: docker stack list