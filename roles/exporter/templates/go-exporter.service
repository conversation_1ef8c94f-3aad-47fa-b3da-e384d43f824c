[Unit]
Description=go-exporter

After=network-online.target local-fs.target time-sync.target
Wants=network-online.target local-fs.target time-sync.target


[Service]
LimitNOFILE=1048576
LimitNPROC=1048576
EnvironmentFile=-/etc/environment
ExecStartPre=-/usr/bin/docker rm go-exporter
ExecStart=-/usr/bin/docker run -p 9105:9105  --network host --name go-exporter -t tianwen1:5000/goexporter:latest
ExecStop=-/usr/bin/docker stop go-exporter
KillMode=none
Restart=on-failure
RestartSec=10s
TimeoutStartSec=120
TimeoutStopSec=120
StartLimitInterval=30min
StartLimitBurst=5

[Install]
WantedBy=default.target
