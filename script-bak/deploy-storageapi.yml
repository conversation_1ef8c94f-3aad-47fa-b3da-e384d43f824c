---
- name: deploy storageapi
  hosts: managers
  vars:
    src_docker_compose: docker-compose.yml
    dest_docker_compose: /etc/thestack/compose/docker-compose-storageapi.yml
    stack_name: storageapi
    is_remove: "{{ remove }}"
  tasks:

    - name: Print install_status
      debug:
        msg: "{{ is_remove }}"

    - name: install storageapi
      include_role:
        name: storageapi
      when:  is_remove == 'false'

    - name: uninstall storageapi
      shell: docker stack rm storageapi
      when: "is_remove == 'true'"
      run_once: true  
