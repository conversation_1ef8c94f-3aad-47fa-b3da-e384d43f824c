#!/bin/bash

# 获取config名称
# echo "请输入要更新的config名称："
# read config_name

# # # 获取新配置文件路径
# echo "请输入新的配置文件路径："
# read new_config_file

# 定义配置项
config_name="$1"; # 需要更新的配置项名称
sleep 1
config_file_name=${config_name%%_*};   
sleep 1
new_config_file="$2" # 新配置文件的路径
sleep 1
echo "config_name $config_name";
sleep 1
echo "config_file_name $config_file_name";
sleep 1
echo "new_config_file $new_config_file";
sleep 1



#获取 所有 services name
services_name_list=$(docker config se $1);
sleep 1
echo "services_name_list $services_name_list";
sleep 1
#删除所有 service
docker service rm $services_name_list;
sleep 1
echo "docker service rm $services_name_list";
sleep 1
#删除 config 
docker config rm  $1;
sleep 1
echo "docker config rm  $1";
sleep 1
#创建所有config
docker config create $1  $2;
sleep 1
echo "docker config create $1  $2";
sleep 1



#services_name_list="thecephapi_s1 thecephapi1_s2 thecephapi2_s thecephapi3_s99"
stack_name_list=$(echo "$services_name_list" | tr ' ' '\n' | awk -F'_' '{print $1}' | sort | uniq);
for stack_name in $stack_name_list; do
    # 取堆叠名称前缀，例如 thecephapi
    #stack_name=${services_name%%_*}
    echo "stack_name $stack_name";
    sleep 1
    echo "stack_name_list $stack_name_list";
    sleep 1
    # 运行创建堆叠的命令，例如 docker stack create thecephapi
    docker stack deploy -c "/etc/thestack/compose/docker-compose-${stack_name}.yml" ${stack_name};
    sleep 1
    echo " docker stack deploy --compose-file "/etc/thestack/compose/docker-compose-${stack_name}.yml" ${stack_name}";
    sleep 1
done



#####方式二
#!/bin/bash

# # 获取config名称
# echo "请输入要更新的config名称："
# read config_name

# # 获取新配置文件路径
# echo "请输入新的配置文件路径："
# read new_config_file


# # 定义配置项
# config_name="thecephapi" # 需要更新的配置项名称
# new_config_file="/root/projects/thecephapi/config.yaml" # 新配置文件的路径

# tmep_config_name="thecephapi_temp"
# # 备份当前配置项
# docker config create $tmep_config_name.bak $new_config_file

# # 创建临时目录和文件
# timestamp=$(date +"%Y%m%d_%H%M%S")
# temp_dir="/tmp/docker-config-update-$timestamp"
# mkdir $temp_dir
# temp_config="$temp_dir/$config_name"

# # 获取当前config
# docker config inspect $config_name > $temp_config

# # 合并新配置到临时文件
# cat $new_config_file >> $temp_config

# # 使用Docker命令更新config
# docker config create $config_name $temp_config

# # 找出使用该config的所有service，并逐个更新它们
# for service in $(docker service ls --format "{{.Name}}"); do
#     config_targets=$(docker service inspect --format "{{range .Spec.TaskTemplate.ContainerSpec.Configs}}{{if eq .ConfigName \"$config_name\"}}{{.File}}={{.ConfigName}} {{end}}{{end}}" $service)
#     for target in $config_targets; do
#         if echo "$target" | grep -q "="; then
#             target_name=$(echo $target | cut -d'=' -f1)
#             target_path=$(echo $target | cut -d'=' -f2)
#             if [ "$target_path" != "" ]; then
#                 docker service update --config-rm $config_name --config-add source=$temp_config,target=$target_path,mode=0644 $service
#                 echo "$service 已成功更新配置（target: $target_name）"
#             fi
#         fi
#     done
# done

# # 删除临时目录和文件
# rm -rf $temp_dir

# # 删除临时目录和文件
# rm -rf $temp_dir