---
- name: deploy prometheus
  hosts: deploy
  vars:
    src_docker_compose: docker-compose.yml
    dest_docker_compose: /etc/thestack/compose/docker-compose-prometheus.yml

    stack_name: prometheus
    is_remove: "{{ remove }}"
  tasks:

    - name: Print install_status
      debug:
        msg: "{{ is_remove }}"

    - name: install prometheus
      include_role:
        name: "../roles/prometheus"
      when:  is_remove == 'false'

    - name: uninstall prometheus
      shell: docker stack rm prometheus
      when: "is_remove == 'true'"
      run_once: true  
