version: "3.7"
services:
  glc:
    image: 'tianwen1:5000/gotoeasy/glc:latest'
    networks:
      - prod
    # ports:
    #   - '9996:8080'
    environment:
      # 【例】每天一个日志仓，自动维护保留90天
      GLC_STORE_NAME_AUTO_ADD_DATE: 'false'
      GLC_SAVE_DAYS: '90'
      # 【例】开启登录，会话30分钟超时，设定管理员的用户密码、令牌盐、黑白名单组合控制仅内网及指定的IP允许访问
      GLC_ENABLE_LOGIN: 'false'
      GLC_SESSION_TIMEOUT: '30'
      GLC_USERNAME: 'admin'
      GLC_PASSWORD: 'thecloud'
      GLC_TOKEN_SALT: 'YourTokenSalt'
      GLC_WHITE_LIST: '*'
      GLC_BLACK_LIST: '*'
      # 【例】每次查询50条日志，日志全部行都要索引检索（多行时默认只是第一行），开启GZIP压缩
      GLC_PAGE_SIZE: '50'
      GLC_SEARCH_MULIT_LINE: 'true'
      GLC_ENABLE_WEB_GZIP: 'true'
      # 【例】开启秘钥，添加日志数据等操作要校验秘钥
      GLC_ENABLE_SECURITY_KEY: 'false'
      GLC_HEADER_SECURITY_KEY: 'X-Glc-Auth'
      GLC_SECURITY_KEY: 'thecloud'
      # 【例】想快速体验时，开启测试模式，可在页面上点击生成测试数据
      GLC_TEST_MODE: 'false'
    volumes:
      - glc_data:/glogcenter
      - /usr/share/zoneinfo/Asia/Shanghai:/etc/localtime
    deploy:
      mode: replicated
      replicas: 1
      restart_policy:
        condition: any
        delay: 10s
        window: 120s
      resources:
        limits:
          memory: 2g
      placement:
        constraints:
          - node.role==manager
      labels:
        - traefik.enable=true
        - traefik.http.middlewares.glclog_strip.stripprefix.prefixes=/glclog
        - traefik.http.routers.glclog.rule=PathPrefix(`/glclog`)
        - traefik.http.routers.glclog.entrypoints=web,websecure
        - traefik.http.services.glclog.loadbalancer.server.port=8080
        - traefik.http.routers.glclog.middlewares=glclog_strip
        - traefik.http.routers.glclog.tls=true
        - traefik.docker.network=prod

  fluentd:
    image: tianwen1:5000/fluentd:1.18.0-1.0
    networks:
      - prod
    volumes:
      - /usr/share/zoneinfo/Asia/Shanghai:/etc/localtime
      - /var/log/the:/fluentd/log
    configs:
      - source: fluentd_config
        target: /fluentd/etc/fluent.conf
    deploy:
      mode: global
      placement:
        constraints:
          - node.role == manager


configs:
  fluentd_config:
    file:  /etc/thestack/conf/fluentd/fluentd.conf

volumes:
  glc_data:

networks:
  prod:
    external: true
