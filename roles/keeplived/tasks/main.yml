---
- name: Create keeplived config directory
  file:
    path: /etc/thestack/conf/keeplived
    state: directory
    mode: '0755'
    recurse: yes  # 递归创建父目录
    
- name: Upload docker compose file
  template:
    src: "{{ src_docker_compose }}"
    dest: "{{ dest_docker_compose }}"
    force: yes
    mode: '0644'    # 可选：设置文件权限
    directory_mode: '0755'
    backup: yes     # 可选：在覆盖前创建备份

- name: Check if destination dest_keeplived_master_conf file exists
  stat:
    path: "{{ dest_keeplived_master_conf }}"
  register: dest_keeplived_master_conf_file


- name: upload keeplived master conf
  template:
    src: "{{ src_keeplived_master_conf }}"
    dest: "{{ dest_keeplived_master_conf }}"
    force: yes
    mode: '0644'    # 可选：设置文件权限
    directory_mode: '0755'
    backup: yes     # 可选：在覆盖前创建备份
  # when: dest_keeplived_master_conf_file.stat.exists == False

- name: Check if destination dest_keeplived_backup1_conf file exists
  stat:
    path: "{{ dest_keeplived_backup1_conf }}"
  register: dest_keeplived_backup1_conf_file


- name: upload keeplived backup1 conf
  template:
    src: "{{ src_keeplived_backup1_conf }}"
    dest: "{{ dest_keeplived_backup1_conf }}"
    force: yes
    mode: '0644'    # 可选：设置文件权限
    directory_mode: '0755'
    backup: yes     # 可选：在覆盖前创建备份
  # when: dest_keeplived_backup1_conf_file.stat.exists == False


- name: Check if destination dest_keeplived_backup2_conf file exists
  stat:
    path: "{{ dest_keeplived_backup2_conf }}"
  register: dest_keeplived_backup2_conf_file


- name: upload keeplived backup2 conf
  template:
    src: "{{ src_keeplived_backup2_conf }}"
    dest: "{{ dest_keeplived_backup2_conf }}"
    force: yes
    mode: '0644'    # 可选：设置文件权限
    backup: yes     # 可选：在覆盖前创建备份
  # when: dest_keeplived_backup2_conf_file.stat.exists == False

- name: Deploy services
  shell: docker stack deploy -c {{ dest_docker_compose }} {{ stack_name }}
  run_once: true
  
- name: Pause for 10 seconds to wait for services being running
  pause:
    seconds: 10
  run_once: true

- name: Ensure services deployed
  shell: docker service ls
  register: service_output

- name: Output service list
  debug:
    msg: "{{ service_output.stdout_lines }}"

- name: Show stacks
  shell: docker stack list
