---
# ansible-playbook -i inventory.ini deploy-mountceph.yml
#- hosts: controller1
- hosts: managers
  tasks:
    - name: df | grep "/mnt/ceph"
      command: df 
      register: dfresult
    - name: if already mount ceph, Interrupt execution
      fail: msg="check failed"
      when: dfresult.stdout.find('/mnt/ceph') > 0
    - name: debug msg
      debug:
        msg:
          - "{{ inventory_hostname }}"
    
    - name: mkdir /mnt/ceph/
      shell: mkdir /mnt/ceph

    - name: docker exec -it ceph-mds-{{ inventory_hostname }} cat /etc/ceph/ceph.client.admin.keyring
      shell: docker exec -it ceph-mds-{{ inventory_hostname }} cat /etc/ceph/ceph.client.admin.keyring
      register: result
    - set_fact: 
        rid: "{{ result.stdout_lines[1] | regex_search('key = (.*)', '\\1')  }}"
    - name: if umount ceph then mount -t ceph {{ inventory_hostname }}:6789
      shell: mount -t ceph {{ inventory_hostname }}:6789:/ /mnt/ceph -o name=admin,secret={{rid[0]}}
      

- hosts: controller1
  tasks:      
    - name: mkdir /mnt/ceph/mysql
      shell: mkdir /mnt/ceph/mysql
    - name: chmod 777  /mnt/ceph/mysql
      shell: chmod 777 -R /mnt/ceph/mysql
    
    - name: mkdir /mnt/ceph/rabbitmq
      shell: mkdir /mnt/ceph/rabbitmq
    - name: chmod 777 /mnt/ceph/rabbitmq
      shell: chmod 777 -R /mnt/ceph/rabbitmq    
    
    
 
