# my global config
global:
  scrape_interval:     15s # Set the scrape interval to every 15 seconds. Default is every 1 minute.
  evaluation_interval: 15s # Evaluate rules every 15 seconds. The default is every 1 minute.
  # scrape_timeout is set to the global default (10s).

# Alertmanager configuration
alerting:
  alertmanagers:
  - static_configs:
    - targets:
      - alertmanager:9093

# Load rules once and periodically evaluate them according to the global 'evaluation_interval'.
rule_files:
   - "/etc/prometheus/rules/rules.yml"
  # - "second_rules.yml"

# A scrape configuration containing exactly one endpoint to scrape:
# Here it's Prometheus itself.
scrape_configs:
  # The job name is added as a label `job=<job_name>` to any timeseries scraped from this config.

  - job_name: 'node_exporter'
    scrape_interval: 15s
    http_sd_configs:
      - url: http://{{ vip }}:8886/v5/prometheus/service
        refresh_interval: 30s
        # tls_config:
        #   cert_file: /etc/prometheus/certs/client.crt     # 客户端证书
        #   key_file: /etc/prometheus/certs/client.key      # 客户端私钥
        #   ca_file: /etc/prometheus/certs/ca.crt          # CA证书

  - job_name: 'vm_node_exporter'
    scrape_interval: 15s
    http_sd_configs:
      - url: http://{{ vip }}:8886/v5/prometheus/vm/service
        refresh_interval: 30s
