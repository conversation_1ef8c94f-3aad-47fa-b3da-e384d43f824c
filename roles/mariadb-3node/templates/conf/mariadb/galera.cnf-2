[galera]
# Galera 集群配置
wsrep_on=ON
wsrep_provider=/usr/lib/galera/libgalera_smm.so
wsrep_cluster_name=galera_cluster
wsrep_cluster_address="gcomm://mariadb-1,mariadb-2,mariadb-3"
wsrep_sst_method=rsync

# Galera 缓存设置
wsrep_provider_options="gcache.size=1G"

# Binlog 配置
log-bin=mysql-bin
binlog_format=ROW
expire_logs_days=7
log-slave-updates=1
# 确保每个节点使用不同的 server_id
server_id=2  # 在其他节点分别设置为 2, 3

# InnoDB 配置
innodb_buffer_pool_size=1G
innodb_flush_log_at_trx_commit=2
innodb_autoinc_lock_mode=2

# 基本 MariaDB 设置
bind-address=0.0.0.0
default_storage_engine=InnoDB

# Binlog 优化设置
sync_binlog=0               # 对性能影响较小的配置
max_binlog_size=100M        # binlog 文件大小限制
binlog_cache_size=4M        # 事务内存缓存大小
binlog_stmt_cache_size=1M   # 非事务语句缓存大小