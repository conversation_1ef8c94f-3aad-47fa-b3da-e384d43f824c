---
- name: deploy conf
  hosts: managers
  vars:
    src_conf: conf
    dest_conf: /etc/thestack/
    src_docker_compose: docker-compose.yml
    dest_docker_compose: /etc/thestack/compose/docker-compose-conf.yml
    src_prometheus_conf: prometheus.yml.j2
    dest_prometheus_conf: /etc/thestack/conf/prometheus/prometheus.yml
    src_traefik_routter_conf: tcp_routers.yml.j2
    dest_traefik_routter_conf: /etc/thestack/conf/traefik/tcp_routers.yml
    src_haproxy_conf: haproxy.cfg.j2
    dest_haproxy_conf: /etc/thestack/conf/haproxy/haproxy.cfg
    src_theweb_conf: settings.py.j2
    dest_theweb_conf: /etc/thestack/conf/theweb/settings.py
    src_keeplived_conf: keeplived.conf.j2
    dest_keeplived_conf: /etc/thestack/conf/keeplived/keeplived.conf
    src_storageapi_conf: storageapi.ini.j2
    dest_storageapi_conf: /etc/thestack/conf/storageapi/storageapi.ini
    src_thelicense_conf: thelicense.ini.j2
    dest_thelicense_conf: /etc/thestack/conf/thelicense/thelicense.ini
    src_thecss_conf: application.properties.j2
    dest_thecss_conf: /etc/thestack/conf/thecephapi/application.properties
    src_thecephapi_conf: thecephapi.yml.j2
    dest_thecephapi_conf: /etc/thestack/conf/thecephapi/thecephapi.yml
    src_thedeskapi_conf: config.ini.j2
    dest_thedeskapi_conf: /etc/thestack/conf/thedesk/config.ini
    src_theguard_conf: theguard.ini.j2
    dest_theguard_conf: /etc/thestack/conf/theguard/config.ini
    src_thedesk_conf: settings.py.j2
    dest_thedesk_conf: /etc/thestack/conf/thedesk/settings.py
    src_usbserver_conf: usbserver.ini.j2
    dest_usbserver_conf: /etc/thestack/conf/usbserver/config.ini
    src_thecloudapi_conf: app.ini.j2
    dest_thecloudapi_conf: /etc/thestack/conf/thecloudapi/app.ini
    src_theauth_conf: theauth.ini.j2
    dest_theauth_conf: /etc/thestack/conf/theauth/config.ini
    src_openstack_sh_conf: openstack.sh.j2
    dest_openstack_sh_conf: /root/cmd/openstack.sh
    stack_name: theconf
  tasks:

    #检测docker版本
    - name: Check if node is in Docker swarm cluster
      shell: 'docker --version'
      register: docker_swarm_node_state

    - name: Print key-value pairs
      debug:
        msg: "{{ docker_swarm_node_state.stdout }}"
      
    #非幂等性安装
    - name: Include role config_reload
      include_role:
        name: config
      when: docker_swarm_node_state.stdout not in docker_version_re

    #幂等性安装
    - name: Include role config
      include_role:
        name: config_reload
      when: docker_swarm_node_state.stdout in docker_version_re