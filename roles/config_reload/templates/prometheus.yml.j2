# my global config
global:
  scrape_interval:     15s # Set the scrape interval to every 15 seconds. Default is every 1 minute.
  evaluation_interval: 15s # Evaluate rules every 15 seconds. The default is every 1 minute.
  # scrape_timeout is set to the global default (10s).

# Alertmanager configuration
alerting:
  alertmanagers:
  - static_configs:
    - targets:
      - alertmanager:9093

# Load rules once and periodically evaluate them according to the global 'evaluation_interval'.
rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

# A scrape configuration containing exactly one endpoint to scrape:
# Here it's Prometheus itself.
scrape_configs:
  # The job name is added as a label `job=<job_name>` to any timeseries scraped from this config.
  - job_name: 'prometheus'

    # metrics_path defaults to '/metrics'
    # scheme defaults to 'http'.
    scrape_interval: 5s
    static_configs:
      - targets: ['prometheus:9090']

  - job_name: 'node-exporter'
    static_configs:
    {% for host in  groups.get('docker-nodes')  %}
  - targets: ['{{ hostvars[host]['ansible_host'] }}:9100']
    {% endfor %}

  - job_name: 'disk-exporter'
    static_configs:
    {% for host in  groups.get('docker-nodes')  %}
  - targets: ['{{ hostvars[host]['ansible_host'] }}:9178']
    {% endfor %}

  - job_name: 'qemu'
    static_configs:
    {% for host in  groups.get('docker-nodes')  %}
  - targets: ['{{ hostvars[host]['ansible_host'] }}:9177']
    {% endfor %}

  - job_name: 'ceph'
    static_configs:
    - targets: ['{{ hostvars[groups.get('docker-nodes')[0]]['ansible_host'] }}:9283']

  - job_name: 'openstackwindows'
    openstack_sd_configs:
      - role: 'instance'
        region: 'RegionOne'
        identity_endpoint: 'http://{{ vip }}:5000/v3/'
        domain_name: 'default'
        userid: 'ed7e9ee3c7d44ca8928b6b484dbdfd30'
        application_credential_id: 'aaab221b1c8f4030bf75ba7103f0bcfa'
        application_credential_secret: '888f48628678514e909ad3'
        port: 9182
    relabel_configs:
      - action: labelmap
        regex: __meta_openstack_(.+)
  - job_name: 'openstack'
    openstack_sd_configs:
      - role: 'instance'
        region: 'RegionOne'
        identity_endpoint: 'http://{{ vip }}:5000/v3/'
        domain_name: 'default'
        userid: 'ed7e9ee3c7d44ca8928b6b484dbdfd30'
        application_credential_id: 'aaab221b1c8f4030bf75ba7103f0bcfa'
        application_credential_secret: '888f48628678514e909ad3'
    relabel_configs:
      # Keep only active instances
      - source_labels: [__meta_openstack_instance_status]
        action: keep
        regex: ACTIVE
      # Keep only instances which are flagged for scraping
      - source_labels: [__meta_openstack_tag_prometheus_io_scrape]
        action: keep
        regex: 'true'
      # Update the scraping port if required
      - source_labels:
        - __address__
        - __meta_openstack_tag_prometheus_io_port
        action: replace
        regex: ([^:]+)(?::\d+)?;(\d+)
        replacement: $1:$2
        target_label: __address__
      # Replace the default instance by the OpenStack instance name
      - source_labels: [__meta_openstack_instance_name]
        target_label: instance

  - job_name: 'openstack_hypervisors'
    openstack_sd_configs:
      - role: 'hypervisor'
        region: 'RegionOne'
        identity_endpoint: 'http://{{ vip }}:5000/v3/'
        domain_name: 'default'
        userid: 'ed7e9ee3c7d44ca8928b6b484dbdfd30'
        application_credential_id: 'aaab221b1c8f4030bf75ba7103f0bcfa'
        application_credential_secret: '888f48628678514e909ad3'
        port: 9100
    relabel_configs:
      - action: labelmap
        regex: __meta_openstack_(.+)