---
- name: deploy dashboard
  hosts: managers
  vars:
    src_docker_compose: docker-compose.yml
    dest_docker_compose: /etc/thestack/compose/docker-compose-dashboard.yml
    stack_name: dashboard
    is_remove: "{{ remove }}"
  tasks:

    - name: Print install_status
      debug:
        msg: "{{ is_remove }}"

    - name: install dashboard
      include_role:
        name: dashboard
      when:  is_remove == 'false'

    - name: uninstall dashboard
      shell: docker stack rm dashboard
      when: "is_remove == 'true'"
      run_once: true  
