---

#判断节点是否是集群节点
- name: Check if node is in Docker swarm cluster 
  shell: 'docker info --format "{% raw %}{{.Swarm.LocalNodeState}}{% endraw %}"' 
  register: docker_swarm_node_state

- name: Handle cluster status
  debug:
    msg: "group_names: {{ group_names }} inventory_hostname: {{inventory_hostname}} groups['managers']: {{ groups['managers'] }}    docker_swarm_node_state.stdout:  {{docker_swarm_node_state.stdout}}"


#获取Manager节点join token
- name: Get the Manager join-token
  shell: docker swarm join-token --quiet manager
  register: manager_token
  tags: swarm
  changed_when: false
  delegate_to: "{{ groups['managers'][0] }}"
  delegate_facts: true
  when: "inventory_hostname != groups['managers'][0] 
    and docker_swarm_node_state.stdout == 'inactive'
    and inventory_hostname in groups['managers']"



#增加Manager节点
- name: Add the Manager 
  shell:  "docker swarm join --token {{ manager_token.stdout  }} {{ hostvars['controller1']['ansible_host'] }}:2377"
  when: "inventory_hostname != groups['managers'][0]
    and docker_swarm_node_state.stdout == 'inactive'
    and inventory_hostname in groups['managers']"





#获取worker节点join token
- name: Get the Worker join-token
  shell: docker swarm join-token --quiet worker
  register: worker_token
  tags: swarm
  changed_when: false
  delegate_to: "{{ groups['managers'][0] }}"
  delegate_facts: true
  when: "inventory_hostname != groups['managers'][0] 
    and docker_swarm_node_state.stdout == 'inactive'
    and inventory_hostname in groups['workers']"


#增加worker节点
- name: Add the Worker 
  shell:  "docker swarm join --token {{ worker_token.stdout  }} {{ hostvars['controller1']['ansible_host'] }}:2377"
  when: "inventory_hostname != groups['managers'][0]
    and docker_swarm_node_state.stdout == 'inactive'
    and inventory_hostname in groups['workers']"

- name: add exporter in new swarm  node
  include_role:
    name: exporter
  when: "inventory_hostname != groups['managers'][0]
    and docker_swarm_node_state.stdout == 'inactive'"

- name: Copy directory
  ansible.builtin.copy:
    src: ../../../cmd
    dest: /root/
    owner: root
    group: root
    mode: "0755"
  when: "inventory_hostname != groups['managers'][0]
    and docker_swarm_node_state.stdout == 'inactive'"