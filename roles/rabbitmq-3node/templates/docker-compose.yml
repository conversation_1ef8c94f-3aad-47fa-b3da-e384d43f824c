version: '3.8'

services:
  rabbitmq1:
    image: rabbitmq:3.12-management
    hostname: rabbitmq1
    deploy:
      placement:
        constraints:
          - node.labels.rabbitmq_node==1
      labels:
        - "traefik.enable=true"
        # AMQP配置
        - "traefik.tcp.routers.rabbitmq1.rule=HostSNI(`*`)"
        - "traefik.tcp.routers.rabbitmq1.entrypoints=rabbitmq"
        - "traefik.tcp.routers.rabbitmq1.service=rabbitmq-service"
        - "traefik.tcp.services.rabbitmq-service.loadbalancer.server.port=5672"
        # 管理界面配置
        - "traefik.http.routers.rabbitmq1-mgmt.rule=HostRegexp(`{any:.*}`)"
        - "traefik.http.routers.rabbitmq1-mgmt.entrypoints=rabbitmq-mgmt"
        - "traefik.http.routers.rabbitmq1-mgmt.service=rabbitmq-mgmt-service"
        - "traefik.http.services.rabbitmq-mgmt-service.loadbalancer.server.port=15672"
    configs:
      - source: rabbitmq.conf
        target: /etc/rabbitmq/rabbitmq.conf
        mode: 0444
      - source: enabled_plugins
        target: /etc/rabbitmq/enabled_plugins
        mode: 0444
    environment:
      - RABBITMQ_ERLANG_COOKIE=QAVCXBJZTMQNQJEYYWQN
      - RABBITMQ_DEFAULT_USER=admin
      - RABBITMQ_DEFAULT_PASS=admin123
    volumes:
      - rabbitmq1_data:/var/lib/rabbitmq
    # ports:
    #   - "5672:5672"
    #   - "15672:15672"
    networks:
      - prod
      

  rabbitmq2:
    image: rabbitmq:3.12-management
    hostname: rabbitmq2
    deploy:
      placement:
        constraints:
          - node.labels.rabbitmq_node==2
      labels:
        - "traefik.enable=true"
        # AMQP配置
        - "traefik.tcp.routers.rabbitmq2.rule=HostSNI(`*`)"
        - "traefik.tcp.routers.rabbitmq2.entrypoints=rabbitmq"
        - "traefik.tcp.routers.rabbitmq2.service=rabbitmq-service"
        - "traefik.tcp.services.rabbitmq-service.loadbalancer.server.port=5672"
        # 管理界面配置
        - "traefik.http.routers.rabbitmq2-mgmt.rule=HostRegexp(`{any:.*}`)"
        - "traefik.http.routers.rabbitmq2-mgmt.entrypoints=rabbitmq-mgmt"
        - "traefik.http.routers.rabbitmq2-mgmt.service=rabbitmq-mgmt-service"
        - "traefik.http.services.rabbitmq-mgmt-service.loadbalancer.server.port=15672"

    configs:
      - source: rabbitmq.conf
        target: /etc/rabbitmq/rabbitmq.conf
        mode: 0444
      - source: enabled_plugins
        target: /etc/rabbitmq/enabled_plugins
        mode: 0444
    environment:
      - RABBITMQ_ERLANG_COOKIE=QAVCXBJZTMQNQJEYYWQN
      - RABBITMQ_DEFAULT_USER=admin
      - RABBITMQ_DEFAULT_PASS=admin123
    volumes:
      - rabbitmq2_data:/var/lib/rabbitmq
    # ports:
    #   - "5673:5672"
    #   - "15673:15672"
    networks:
      - prod

  rabbitmq3:
    image: rabbitmq:3.12-management
    hostname: rabbitmq3
    deploy:
      placement:
        constraints:
          - node.labels.rabbitmq_node==3
      labels:
        - "traefik.enable=true"
        # AMQP配置
        - "traefik.tcp.routers.rabbitmq3.rule=HostSNI(`*`)"
        - "traefik.tcp.routers.rabbitmq3.entrypoints=rabbitmq"
        - "traefik.tcp.routers.rabbitmq3.service=rabbitmq-service"
        - "traefik.tcp.services.rabbitmq-service.loadbalancer.server.port=5672"
        # 管理界面配置
        - "traefik.http.routers.rabbitmq3-mgmt.rule=HostRegexp(`{any:.*}`)"
        - "traefik.http.routers.rabbitmq3-mgmt.entrypoints=rabbitmq-mgmt"
        - "traefik.http.routers.rabbitmq3-mgmt.service=rabbitmq-mgmt-service"
        - "traefik.http.services.rabbitmq-mgmt-service.loadbalancer.server.port=15672"

    configs:
      - source: rabbitmq.conf
        target: /etc/rabbitmq/rabbitmq.conf
        mode: 0444
      - source: enabled_plugins
        target: /etc/rabbitmq/enabled_plugins
        mode: 0444
    environment:
      - RABBITMQ_ERLANG_COOKIE=QAVCXBJZTMQNQJEYYWQN
      - RABBITMQ_DEFAULT_USER=admin
      - RABBITMQ_DEFAULT_PASS=admin123
    volumes:
      - rabbitmq3_data:/var/lib/rabbitmq
    # ports:
    #   - "5674:5672"
    #   - "15674:15672"
    networks:
      - prod

configs:
  rabbitmq.conf:
    external: true
  enabled_plugins:
    external: true

volumes:
  rabbitmq1_data:
  rabbitmq2_data:
  rabbitmq3_data:

networks:
  prod:
    name: prod
    external: true
