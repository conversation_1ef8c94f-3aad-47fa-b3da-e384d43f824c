---
- name: deploy dayu
  hosts: managers
  vars:
    src_docker_compose: docker-compose.yml
    dest_docker_compose: /etc/thestack/compose/docker-compose-dayu.yml
    
    src_conductor_conf: conductor.yml.j2
    dest_conductor_conf: /etc/thestack/conf/dayu/conductor.yml
    
    src_scheduler_conf: scheduler.yml.j2
    dest_scheduler_conf: /etc/thestack/conf/dayu/scheduler.yml
    
    src_trigger_conf: trigger.yml.j2
    dest_trigger_conf: /etc/thestack/conf/dayu/trigger.yml
    
    src_executor_conf: executor.yml.j2
    dest_executor_conf: /etc/thestack/conf/dayu/executor.yml
    
    src_recommend_conf: recommend.yml.j2
    dest_recommend_conf: /etc/thestack/conf/dayu/recommend.yml
        
    stack_name: dayu
    is_remove: "{{ remove }}"
  tasks:

    - name: Print install_status
      debug:
        msg: "{{ is_remove }}"

    - name: install dayu
      include_role:
        name: dayu
      when:  is_remove == 'false'

    - name: uninstall dayu
      shell: docker stack rm dayu
      when: "is_remove == 'true'"
      run_once: true  
