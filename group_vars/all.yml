copy_ssh_enable: true

ansible_become: yes 
ansible_become_method: sudo
ansible_become_pass: "maojj009"

openstack_password: "thecloud2015.1"
ceph_webloginpwd: "p@ssw0rd"
cluster_network: "***********/24"
custom_network_interface: "eth0"
disks_select: "auto"
domain: ""
id: 0
kolla_external_vip_address: "*************"
kolla_internal_vip_address: "*************"
manage_network_interface: "eth0"
monitor_interface: "eth0"
network_interface: "eth0"
neutron_external_interface: "eth0"
project_id: 1
public_network: "***********/24"
radosgw_interface: "eth0"
storage_cluster_network: "***********/24"
storage_network_interface: "eth0"
storage_public_network: "***********/24"
vip: "**************"
remove: "false"
mysql: true # kingbase, dqlite
kingbase: false # kingbase, dqlite

docker_version_re:
  - Docker version 24.0.0-rc.2-11-g8878bf8c5e.m, build 8878bf8c5e
  # - Docker version 25.0.0-rc.2-11-g8878bf8c5e.m, build 8878bf8c5e
  # - Docker version 26.0.0-rc.2-11-g8878bf8c5e.m, build 8878bf8c5e

use_repo: true

ac_enable: "false"



