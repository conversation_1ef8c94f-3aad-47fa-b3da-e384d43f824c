<source>
    @type tail
    encoding utf-8
    from_encoding utf-8
    path /fluentd/log/the*.log 
    pos_file /fluentd/log/thelog.pos
    tag fluentd.log.the.log
    path_key path
    format /^(?<exectime>\d{4}/\d{1,2}/\d{1,2}\s\d{1,2}:\d{1,2}:\d{1,2}) ?;(?<module>.*?);(?<loglevel>.*?);(?<lineno>.*?);(?<operator>.*?);(?<action>.*?);(?<object>.*?);(?<role>.*?);(?<result>.*?)$/
</source>

<filter fluentd.log.the.log>
  @type record_transformer
  <record>
    client_address "#{IPSocket.getaddress(Socket.gethostname)}"
  </record>
</filter>

<match fluentd.log.the.log>
    @type copy
    <store>
      @type elasticsearch
      hosts elasticsearch:9200
      logstash_format true
      logstash_prefix thelog
      logstash_dateformat %Y.%m.%d
      flush_interval 5s
    </store>
    <store>
	@type stdout 
    </store>
</match>
