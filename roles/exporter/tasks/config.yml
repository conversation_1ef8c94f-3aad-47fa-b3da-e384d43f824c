---
- name: Copy node-exporter systemd file
  template:
    src: "node-exporter.service"
    dest: "/etc/systemd/system/node-exporter.service"    
    backup: yes

- name: Copy libvirt-exporter systemd file
  template:
    src: "libvirt-exporter.service"
    dest: "/etc/systemd/system/libvirt-exporter.service"    
    backup: yes

# - name: Copy disk-exporter systemd file
#   template:
#     src: "disk-exporter.service"
#     dest: "/etc/systemd/system/disk-exporter.service"
#     backup: yes

- name: Copy restart-kolla systemd file
  template:
    src: "restart-kolla.service"
    dest: "/etc/systemd/system/restart-kolla.service"
    backup: yes

- name: Copy go-exporter systemd file
  template:
    src: "go-exporter.service"
    dest: "/etc/systemd/system/go-exporter.service"    
    backup: yes

- name: Copy usbserver systemd file
  template:
    src: "usbserver.service"
    dest: "/etc/systemd/system/usbserver.service"    
    backup: yes
  
- name: Started exporter service
  service:
    name: "{{ item }}"
    state: started
    enabled: yes 
  loop:
    - node-exporter
    - libvirt-exporter
    # - disk-exporter
    - restart-kolla
    - go-exporter
    - usbserver
    