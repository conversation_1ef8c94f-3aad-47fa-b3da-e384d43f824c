version: "3.7"
services:
  canal-server:
    image: tianwen1:5000/canal/canal-server:v1.1.8
    container_name: canal-server
    # ports:
    #   - "11111:11111"
    environment:
      - CANAL_IP=canal-server
      - CANAL_PORT=11111
      - CANAL_DESTINATIONS=example
      - CANAL_INSTANCE_MYSQL_SLAVED_ID=123
      - CANAL_INSTANCE_MASTER_ADDRESS={{ vip }}:3306
      - CANAL_INSTANCE_DBUSERNAME=root
      - CANAL_INSTANCE_DBPASSWORD=thecloud2015.1
      - CANAL_INSTANCE_FILTER_REGEX=hci_db\\.users
    volumes:
      - canal_example:/home/<USER>/canal-server/conf/example:rw
      - /etc/thestack/conf/canal/instance.properties:/home/<USER>/canal-server/conf/example/instance.properties:rw
    # configs:
    #   - source: canal_instance.properties
    #     target: /home/<USER>/canal-server/conf/example/instance.properties
    networks:
      - prod
    deploy:
      mode: replicated
      restart_policy:
        condition: any
        delay: 10s
        max_attempts: 3
        window: 180s
      placement:
        constraints:
          - node.role==manager
      labels:
        - traefik.enable=true
        - traefik.docker.network=traefik_prod
        - traefik.tcp.routers.canal.rule=HostSNI(`*`)
        - traefik.tcp.routers.canal.entrypoints=canal
        - traefik.tcp.routers.canal.tls=false
        - traefik.tcp.routers.canal.service=canal
        - traefik.tcp.services.canal.loadbalancer.server.port=11111

# configs:
#   canal_instance.properties:
#     file: /etc/thestack/conf/canal/instance.properties

volumes:
  canal_example:

networks:
  prod:
    external: true
