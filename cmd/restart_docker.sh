#!/bin/bash

echo -e "==========================================\n"
echo -e "1---重新启动所有kolla服务"
echo -e "2---选择要重新启动的kolla组件服务"
echo -e "==========================================\n\n"
read -p "请选择你要做的事：" num

if [ $num -eq 1 ]; then
#直接重启所有kolla服务
    docker ps -a | grep kolla | awk '{ print $1 }' | xargs docker restart
    echo "服务重启完成，docker已重启"
    echo -e "------------------------------------------\n"
elif [ $num -eq 2 ]; then
    echo -e "==========================================\n"
    echo -e "kolla组件服务包括有："
    echo -e "1---nova"
    echo -e "2---neutron"
    echo -e "3---keystone"
    echo -e "4---cinder"
    echo -e "5---placement"
    echo -e "6---glance"
    echo -e "7---horizon"
    echo -e "8---memcache"
    echo -e "9---cron"
    echo -e "10---kolla-toolbox"
    echo -e "==========================================\n\n"
    read -p "请选择你要重启的服务：" kolla_num

#将kolla的所有组件写成数组，方便调用
    kolla_array=("nova" "neutron" "keystone" "cinder" "placement" "glance" "horizon" "memcache" "cron" "kolla-toolbox" )

#重启选择的对应组件服务
    if [ "$kolla_num" -ge 1 ] && [ "$kolla_num" -le "${#kolla_array[@]}" ]; then
        echo -e "正在重启kolla的${kolla_array[$kolla_num-1]}组件服务\n"
        docker ps -a | grep kolla/"${kolla_array[$kolla_num-1]}" | awk '{ print $1 }' | xargs docker restart
        echo "${kolla_array[$kolla_num-1]}组件服务重启完成，docker已重启"
        echo -e "------------------------------------------\n"
    else
        echo "输入有误，退出脚本！！"
        exit
    fi

else
    echo "输入有误，退出脚本！！"
    exit
fi
