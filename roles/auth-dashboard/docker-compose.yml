version: "3.7"
services:
  theauth:
    image: tianwen1:5000/theauth:latest
    hostname: whoami
    networks:
      - traefik_prod
      - mariadb_dbnet
    volumes:
      - /var/log/the:/var/log/the
      - ./data/theauth/config.ini:/etc/vdiauth/config.ini
    deploy:
      replicas: 1
      labels:
        - traefik.enable=true
        - traefik.http.middlewares.theauth_strip.stripprefix.prefixes=/login
        - traefik.http.routers.theauth.rule=PathPrefix(`/login`)
        - traefik.http.services.theauth.loadbalancer.server.port=8088
        - traefik.http.routers.theauth.middlewares=theauth_strip
        - traefik.docker.network=traefik_prod
  
networks:
  traefik_prod:
    external: true
  mariadb_dbnet:
    external: true