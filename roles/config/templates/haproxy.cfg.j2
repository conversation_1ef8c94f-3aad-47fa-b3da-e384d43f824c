global
    log stdout local0
    maxconn 4096

defaults
    log global
    mode http
    timeout connect 5000ms
    timeout client 50000ms
    timeout server 50000ms

listen status
    bind {{ vip }}:8932  transparent
    mode http
    stats enable
    stats uri /
    stats refresh 15s
    stats realm Haproxy\ Stats
    stats auth admin:admin

listen cephapi
    mode http
    bind {{ vip }}:8443 transparent
    capture cookie vgnvisitor= len 32
    cookie SERVERID insert indirect nocache
    option forwardfor
    mode http
    option forwardfor
    option httpchk  GET /
    http-check expect status 200
    option httpclose
    {% for host in  groups.get('docker-nodes')  %}
server {{ host }} {{ hostvars[host]['ansible_host'] }}:8443 cookie {{ host }} check inter 2000 rise 2 fall 5
    {% endfor %}

listen ceph-exporter
    mode http
    bind {{ vip }}:9284 transparent
    capture cookie vgnvisitor= len 32
    cookie SERVERID insert indirect nocache
    option forwardfor
    option httpchk  GET /metrics
    http-check expect status 200
    http-check expect !rstring ^0$
    option httpclose
    {% for host in  groups.get('docker-nodes')  %}
server {{ host }} {{ hostvars[host]['ansible_host'] }}:9283 cookie {{ host }} check inter 2000 rise 2 fall 5
    {% endfor %}

listen nova_novncproxy
    bind {{ vip }}:6080 transparent
    balance source
    option tcpka
    option tcplog
    {% for host in  groups.get('docker-nodes')  %}
server {{ host }} {{ hostvars[host]['ansible_host'] }}:6080 check inter 2000 rise 2 fall 5
    {% endfor %}

listen memcached-service
    bind {{ vip }}:11211 transparent
    balance source
    option tcpka
    option tcplog
    {% for host in  groups.get('docker-nodes')  %}
server {{ host }} {{ hostvars[host]['ansible_host'] }}:11211 check inter 2000 rise 2 fall 5
    {% endfor %}


listen keystone-service
    mode http
    bind {{ vip }}:5000 transparent
    capture cookie vgnvisitor= len 32
    cookie SERVERID insert indirect nocache
    option forwardfor
    option httpchk  GET /
    option httpclose
    {% for host in  groups.get('docker-nodes')  %}
server {{ host }} {{ hostvars[host]['ansible_host'] }}:5000 cookie {{ host }} check inter 2000 rise 2 fall 5
    {% endfor %}


listen glance-api-service
    mode http
    bind {{ vip }}:9292 transparent
    capture cookie vgnvisitor= len 32
    cookie SERVERID insert indirect nocache
    option forwardfor
    option httpchk  GET /
    option httpclose
    {% for host in  groups.get('docker-nodes')  %}
server {{ host }} {{ hostvars[host]['ansible_host'] }}:9292 cookie {{ host }} check inter 2000 rise 2 fall 5
    {% endfor %}

    
listen placement-service
    mode http
    bind {{ vip }}:8780 transparent
    capture cookie vgnvisitor= len 32
    cookie SERVERID insert indirect nocache
    option forwardfor
    option httpchk  GET /
    option httpclose
    {% for host in  groups.get('docker-nodes')  %}
server {{ host }} {{ hostvars[host]['ansible_host'] }}:8780 cookie {{ host }} check inter 2000 rise 2 fall 5
    {% endfor %}
    

listen nova-api-service
    mode http
    bind {{ vip }}:8774 transparent
    capture cookie vgnvisitor= len 32
    cookie SERVERID insert indirect nocache
    option forwardfor
    option httpchk  GET /
    option httpclose
    {% for host in  groups.get('docker-nodes')  %}
server {{ host }} {{ hostvars[host]['ansible_host'] }}:8774 cookie {{ host }} check inter 2000 rise 2 fall 5
    {% endfor %}
        
listen nova-metadata-api-service
    mode http
    bind {{ vip }}:8775 transparent
    capture cookie vgnvisitor= len 32
    cookie SERVERID insert indirect nocache
    option forwardfor
    option httpchk  GET /
    option httpclose
    {% for host in  groups.get('docker-nodes')  %}
server {{ host }} {{ hostvars[host]['ansible_host'] }}:8775 cookie {{ host }} check inter 2000 rise 2 fall 5
    {% endfor %}


listen neutron-server
    mode http
    bind {{ vip }}:9696 transparent
    capture cookie vgnvisitor= len 32
    cookie SERVERID insert indirect nocache
    option forwardfor
    option httpchk  GET /
    option httpclose
    {% for host in  groups.get('docker-nodes')  %}
server {{ host }} {{ hostvars[host]['ansible_host'] }}:9696 cookie {{ host }} check inter 2000 rise 2 fall 5
    {% endfor %}


listen cinder-api-service
    mode http
    bind {{ vip }}:8776 transparent
    capture cookie vgnvisitor= len 32
    cookie SERVERID insert indirect nocache
    option forwardfor
    option httpchk  GET /
    option httpclose
    {% for host in  groups.get('docker-nodes')  %}
server {{ host }} {{ hostvars[host]['ansible_host'] }}:8776 cookie {{ host }} check inter 2000 rise 2 fall 5
    {% endfor %}



listen keystone-admin-service
    mode http
    bind {{ vip }}:35357 transparent
    capture cookie vgnvisitor= len 32
    cookie SERVERID insert indirect nocache
    option forwardfor
    option httpchk  GET /
    option httpclose
    {% for host in  groups.get('docker-nodes')  %}
server {{ host }} {{ hostvars[host]['ansible_host'] }}:35357 cookie {{ host }} check inter 2000 rise 2 fall 5
    {% endfor %}


frontend  ceph-prometheus-admin-service
    bind *:9097 transparent
    acl path_api path_beg -i /api
    http-request set-path %[path,regsub(^/apiquery,,)] if path_api
    use_backend api_backend if path_api

backend api_backend
    mode http
    cookie SERVERID insert indirect nocache
    balance source
    option tcpka
    option tcplog
    {% for host in  groups.get('docker-nodes')  %}
server {{ host }} {{ hostvars[host]['ansible_host'] }}:9092 check inter 2000 rise 2 fall 5
    {% endfor %}
