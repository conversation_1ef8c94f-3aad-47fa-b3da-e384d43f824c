{"dashboard": {"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "description": "Windows的Prometheus监控看板展示，增加了资源汇总展示，优化了明细展示。更新支持windows_exporter 0.13.0。", "editable": true, "gnetId": 10467, "graphTooltip": 1, "id": null, "iteration": 1687762754733, "links": [{"icon": "bolt", "tags": [], "targetBlank": true, "title": "Update", "tooltip": "更新当前仪表板", "type": "link", "url": "https://grafana.com/grafana/dashboards/10467"}, {"icon": "question", "tags": [], "targetBlank": true, "title": "GitHub", "tooltip": "查看更多仪表板", "type": "link", "url": "https://github.com/starsliao/Prometheus"}, {"asDropdown": true, "icon": "external link", "tags": [], "targetBlank": true, "type": "dashboards"}], "panels": [{"collapsed": false, "datasource": "Prometheus", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 41, "panels": [], "title": "资源总览（关联JOB项）当前选中主机：【$show_hostname】实例：$instance", "type": "row"}, {"columns": [], "datasource": "Prometheus", "fieldConfig": {"defaults": {}, "overrides": []}, "fontSize": "80%", "gridPos": {"h": 6, "w": 24, "x": 0, "y": 1}, "id": 45, "pageSize": null, "showHeader": true, "sort": {"col": 0, "desc": true}, "styles": [{"$$hashKey": "object:1557", "alias": "操作系统", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "mappingType": 1, "pattern": "product", "preserveFormat": false, "sanitize": false, "thresholds": [], "type": "string", "unit": "short", "valueMaps": []}, {"$$hashKey": "object:1570", "alias": "主机名", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "link": false, "linkTooltip": "", "linkUrl": "", "mappingType": 1, "pattern": "hostname", "preserveFormat": false, "thresholds": [], "type": "string", "unit": "short"}, {"$$hashKey": "object:1609", "alias": "instance(连接到明细)", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "link": true, "linkTargetBlank": false, "linkTooltip": "${__cell_4}", "linkUrl": "/d/Kdh0OoSGz/?var-job=${__cell_5}&var-hostname=All&var-instance=${__cell_4}", "mappingType": 1, "pattern": "instance", "thresholds": [], "type": "string", "unit": "short"}, {"$$hashKey": "object:1622", "alias": "CPU核数", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "mappingType": 1, "pattern": "Value #B", "thresholds": [], "type": "string", "unit": "short"}, {"$$hashKey": "object:1635", "alias": "总内存", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": null, "mappingType": 1, "pattern": "Value #C", "thresholds": [], "type": "number", "unit": "bytes"}, {"$$hashKey": "object:2635", "alias": "运行时间", "align": "auto", "colorMode": "cell", "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": null, "mappingType": 1, "pattern": "Value #D", "thresholds": ["259200", "432000"], "type": "number", "unit": "s"}, {"$$hashKey": "object:1809", "alias": "C盘使用率", "align": "auto", "colorMode": "cell", "colors": ["rgba(50, 172, 45, 0.97)", "rgba(237, 129, 40, 0.89)", "rgba(245, 54, 54, 0.9)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "mappingType": 1, "pattern": "Value #E", "thresholds": ["60", "80"], "type": "number", "unit": "percentunit"}, {"$$hashKey": "object:1821", "alias": "使用最多分区%", "align": "auto", "colorMode": "cell", "colors": ["rgba(50, 172, 45, 0.97)", "rgba(237, 129, 40, 0.89)", "rgba(245, 54, 54, 0.9)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "mappingType": 1, "pattern": "Value #F", "thresholds": ["60", "80"], "type": "number", "unit": "percentunit"}, {"$$hashKey": "object:2286", "alias": "进程数", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "link": false, "mappingType": 1, "pattern": "Value #G", "thresholds": [], "type": "string", "unit": "short"}, {"$$hashKey": "object:2297", "alias": "运行服务", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "mappingType": 1, "pattern": "Value #H", "thresholds": [], "type": "string", "unit": "short"}, {"$$hashKey": "object:3599", "alias": "CPU使用率", "align": "auto", "colorMode": "cell", "colors": ["rgba(50, 172, 45, 0.97)", "rgba(237, 129, 40, 0.89)", "rgba(245, 54, 54, 0.9)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "mappingType": 1, "pattern": "Value #I", "thresholds": ["50", "70"], "type": "number", "unit": "percent"}, {"$$hashKey": "object:3610", "alias": "内存使用率", "align": "auto", "colorMode": "cell", "colors": ["rgba(50, 172, 45, 0.97)", "rgba(237, 129, 40, 0.89)", "rgba(245, 54, 54, 0.9)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "mappingType": 1, "pattern": "Value #J", "thresholds": ["60", "80"], "type": "number", "unit": "percent"}, {"$$hashKey": "object:5929", "alias": "CPU频率", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "mappingType": 1, "pattern": "Value #K", "thresholds": [], "type": "number", "unit": "<PERSON>hz"}, {"$$hashKey": "object:1311", "alias": "", "align": "right", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "decimals": 2, "pattern": "/.*/", "thresholds": [], "type": "hidden", "unit": "short"}], "targets": [{"expr": "windows_os_info{job=~\"$job\"} * on(instance) group_right(product) windows_cs_hostname", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "A"}, {"expr": "time() - windows_system_system_up_time{job=~\"$job\"}", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "D"}, {"expr": "windows_cs_logical_processors{job=~\"$job\"} - 0", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "B"}, {"expr": "avg by (instance) (windows_cpu_core_frequency_mhz{job=~\"$job\"})", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "K"}, {"expr": "100 - (avg by (instance) (irate(windows_cpu_time_total{job=~\"$job\",mode=\"idle\"}[2m])) * 100)", "format": "table", "instant": true, "interval": "", "legendFormat": "CPU使用率", "refId": "I"}, {"expr": "windows_cs_physical_memory_bytes{job=~\"$job\"} - 0", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "C"}, {"expr": "100 - 100 * windows_os_physical_memory_free_bytes{job=~\"$job\"} / windows_cs_physical_memory_bytes{job=~\"$job\"}", "format": "table", "instant": true, "interval": "", "legendFormat": "内存使用率", "refId": "J"}, {"expr": "1 - (windows_logical_disk_free_bytes{job=~\"$job\",volume=~\"C:\"}/windows_logical_disk_size_bytes{job=~\"$job\",volume=~\"C:\"})", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "E"}, {"expr": "max by (instance) (1-windows_logical_disk_free_bytes{job=~\"$job\"}/windows_logical_disk_size_bytes{job=~\"$job\"})", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "F"}, {"expr": "windows_os_processes{job=~\"$job\"}", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "G"}, {"expr": "sum by (instance) (windows_service_state{job=~\"$job\",state=~\"running\"})", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "H"}], "timeFrom": null, "timeShift": null, "title": "$job：服务器资源总览", "transform": "table", "type": "table-old"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "decimals": 1, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 5, "x": 0, "y": 7}, "hiddenSeries": false, "id": 47, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": false, "rightSide": true, "show": false, "sort": "current", "sortDesc": false, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "paceLength": 10, "percentage": false, "pluginVersion": "7.5.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "100 - (avg by (instance) (irate(windows_cpu_time_total{job=~\"$job\",mode=\"idle\"}[2m])) * 100)", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "{{instance}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "各主机CPU使用率", "tooltip": {"shared": true, "sort": 2, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:3535", "format": "percent", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:3536", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {"Free Physical memory": "semi-dark-green", "Free physical memory": "semi-dark-green", "Free virtual memory": "super-light-blue", "Physical memory": "dark-red", "Virtual memory": "dark-blue"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 1, "gridPos": {"h": 6, "w": 5, "x": 5, "y": 7}, "hiddenSeries": false, "hideTimeOverride": false, "id": 49, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": false, "rightSide": true, "show": false, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:3675", "alias": "/.*Physical.*/", "linewidth": 3}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "100.0 - 100 * windows_os_physical_memory_free_bytes{job=~\"$job\"} / windows_cs_physical_memory_bytes{job=~\"$job\"}", "instant": false, "interval": "", "legendFormat": "{{instance}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "各主机内存使用率", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:3688", "format": "percent", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:3689", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 5, "x": 10, "y": 7}, "hiddenSeries": false, "hideTimeOverride": false, "id": 53, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": false, "rightSide": true, "show": false, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": ""}, "expr": "max by (instance) (irate(windows_net_bytes_sent_total{job=~\"$job\",nic!~'isatap.*|VPN.*'}[2m]))*8", "format": "time_series", "functions": [], "group": {"filter": ""}, "hide": false, "host": {"filter": ""}, "interval": "", "intervalFactor": 1, "item": {"filter": ""}, "legendFormat": "{{instance}}_上传", "metric": "mysql_global_status_questions", "mode": 0, "options": {"showDisabledItems": false}, "refId": "B", "step": 10}, {"application": {"filter": ""}, "expr": "-max by (instance) (irate(windows_net_bytes_received_total{job=~\"$job\",nic!~'isatap.*|VPN.*'}[2m]))*8", "format": "time_series", "functions": [], "group": {"filter": ""}, "hide": false, "host": {"filter": ""}, "interval": "", "intervalFactor": 1, "item": {"filter": ""}, "legendFormat": "{{instance}}_下载", "metric": "mysql_global_status_questions", "mode": 0, "options": {"showDisabledItems": false}, "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "各主机最大流量网卡网络详情", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:4930", "format": "bps", "label": "下载　　　　上传", "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:4931", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 2, "fillGradient": 0, "gridPos": {"h": 6, "w": 5, "x": 15, "y": 7}, "hiddenSeries": false, "hideTimeOverride": false, "id": 51, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": false, "rightSide": true, "show": false, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": ""}, "expr": "-max by (instance) (irate(windows_logical_disk_read_bytes_total[2m]))", "format": "time_series", "functions": [], "group": {"filter": ""}, "hide": false, "host": {"filter": ""}, "interval": "", "intervalFactor": 1, "item": {"filter": ""}, "legendFormat": "{{instance}}_读取", "metric": "mysql_global_status_questions", "mode": 0, "options": {"showDisabledItems": false}, "refId": "A", "step": 20}, {"application": {"filter": ""}, "expr": "max by (instance) (irate(windows_logical_disk_write_bytes_total[2m]))", "format": "time_series", "functions": [], "group": {"filter": ""}, "hide": false, "host": {"filter": ""}, "interval": "", "intervalFactor": 1, "item": {"filter": ""}, "legendFormat": "{{instance}}_写入", "metric": "mysql_global_status_questions", "mode": 0, "options": {"showDisabledItems": false}, "refId": "B", "step": 20}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "各主机最大磁盘读写详情", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:5258", "format": "Bps", "label": "读取　　　　写入", "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:5259", "decimals": null, "format": "iops", "label": "", "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 4, "x": 20, "y": 7}, "hiddenSeries": false, "hideTimeOverride": false, "id": 55, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": false, "rightSide": true, "show": false, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": ""}, "expr": "-max by (instance) (irate(windows_logical_disk_reads_total[2m]))", "format": "time_series", "functions": [], "group": {"filter": ""}, "hide": false, "host": {"filter": ""}, "interval": "", "intervalFactor": 1, "item": {"filter": ""}, "legendFormat": "{{instance}}_读取", "metric": "mysql_global_status_questions", "mode": 0, "options": {"showDisabledItems": false}, "refId": "B", "step": 20}, {"application": {"filter": ""}, "expr": "max by (instance) (irate(windows_logical_disk_writes_total[2m]))", "format": "time_series", "functions": [], "group": {"filter": ""}, "hide": false, "host": {"filter": ""}, "interval": "", "intervalFactor": 1, "item": {"filter": ""}, "legendFormat": "{{instance}}_写入", "metric": "mysql_global_status_questions", "mode": 0, "options": {"showDisabledItems": false}, "refId": "C", "step": 20}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "各主机最大磁盘IO详情", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:5486", "format": "iops", "label": "读取　　　　写入", "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:5487", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "datasource": "Prometheus", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 13}, "id": 43, "panels": [], "title": "资源明细【$show_hostname：$instance】", "type": "row"}, {"cacheTimeout": null, "colorBackground": false, "colorPostfix": false, "colorPrefix": false, "colorValue": true, "colors": ["#299c46", "rgba(237, 129, 40, 0.89)", "#d44a3a"], "datasource": "Prometheus", "decimals": 0, "description": "", "fieldConfig": {"defaults": {}, "overrides": []}, "format": "s", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 2, "w": 2, "x": 0, "y": 14}, "id": 33, "interval": "", "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "100%", "prefix": "", "prefixFontSize": "100%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "repeat": "host", "repeatDirection": "v", "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "tableColumn": "{instance=\"*************:9182\", job=\"windows-exporter\"}", "targets": [{"expr": "time() - windows_system_system_up_time{job=~\"$job\",instance=~\"$instance\"}", "instant": false, "interval": "", "legendFormat": "", "refId": "A"}], "thresholds": "", "timeFrom": null, "timeShift": null, "title": "启动时长", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "current"}, {"cacheTimeout": null, "colorBackground": false, "colorPostfix": true, "colorValue": true, "colors": ["#299c46", "rgba(237, 129, 40, 0.89)", "#d44a3a"], "datasource": "Prometheus", "fieldConfig": {"defaults": {}, "overrides": []}, "format": "percent", "gauge": {"maxValue": 100, "minValue": 0, "show": true, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 3, "w": 2, "x": 2, "y": 14}, "id": 19, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": true}, "tableColumn": "", "targets": [{"expr": "100 - (avg(irate(windows_cpu_time_total{job=~\"$job\",instance=~\"$instance\",mode=\"idle\"}[2m])))*100", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "thresholds": "60,90", "title": "CPU使用率", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "", "value": ""}], "valueName": "current"}, {"cacheTimeout": null, "datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 80}, {"color": "red", "value": 90}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 6, "w": 4, "x": 4, "y": 14}, "id": 23, "links": [], "options": {"displayMode": "lcd", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showUnfilled": true, "text": {}}, "pluginVersion": "7.5.2", "targets": [{"expr": "100 - (windows_logical_disk_free_bytes{job=~\"$job\",instance=~\"$instance\"} / windows_logical_disk_size_bytes{job=~\"$job\",instance=~\"$instance\"})*100", "instant": false, "interval": "", "legendFormat": "{{volume}}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "各分区使用率", "type": "bargauge"}, {"aliasColors": {"Received mysqld-exporter:9104": "#0A50A1", "stopped": "#2F575E"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 10, "fillGradient": 0, "gridPos": {"h": 6, "w": 8, "x": 8, "y": 14}, "hiddenSeries": false, "id": 7, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 0, "links": [], "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"application": {"filter": ""}, "expr": "sum(windows_service_state{job=~\"$job\",instance=~\"$instance\"}) by (state)", "format": "time_series", "functions": [], "group": {"filter": ""}, "host": {"filter": ""}, "interval": "", "intervalFactor": 1, "item": {"filter": ""}, "legendFormat": "{{state}}", "mode": 0, "options": {"showDisabledItems": false}, "refId": "A", "step": 5}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "服务状态", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:570", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:571", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "decimals": null, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 8, "x": 16, "y": 14}, "hiddenSeries": false, "id": 27, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": false, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "paceLength": 10, "percentage": false, "pluginVersion": "7.5.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:940", "alias": "进程数", "color": "#B877D9"}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "windows_os_processes{job=~\"$job\",instance=~\"$instance\"}", "instant": false, "interval": "", "legendFormat": "进程数", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "进程数", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:87", "decimals": 0, "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:88", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"cacheTimeout": null, "colorBackground": false, "colorPostfix": false, "colorPrefix": false, "colorValue": true, "colors": ["#299c46", "rgba(237, 129, 40, 0.89)", "#d44a3a"], "datasource": "Prometheus", "decimals": 0, "description": "", "fieldConfig": {"defaults": {}, "overrides": []}, "format": "none", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 2, "w": 2, "x": 0, "y": 16}, "id": 35, "interval": "", "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "100%", "prefix": "", "prefixFontSize": "100%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "repeat": "host", "repeatDirection": "v", "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "tableColumn": "windows_cs_logical_processors{instance=\"*************:9182\", job=\"windows-exporter\"}", "targets": [{"expr": "windows_cs_logical_processors{job=~\"$job\",instance=~\"$instance\"}", "format": "time_series", "instant": false, "interval": "", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "thresholds": "", "timeFrom": null, "timeShift": null, "title": "CPU核数", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "current"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": true, "colors": ["#299c46", "rgba(237, 129, 40, 0.89)", "#d44a3a"], "datasource": "Prometheus", "decimals": null, "fieldConfig": {"defaults": {}, "overrides": []}, "format": "percent", "gauge": {"maxValue": 100, "minValue": 0, "show": true, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 3, "w": 2, "x": 2, "y": 17}, "id": 21, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": true}, "tableColumn": "{instance=\"*************:9182\", job=\"windows-exporter\"}", "targets": [{"expr": "100 - (windows_os_physical_memory_free_bytes{job=~\"$job\",instance=~\"$instance\"} / windows_cs_physical_memory_bytes{job=~\"$job\",instance=~\"$instance\"})*100", "instant": false, "refId": "A"}], "thresholds": "80,90", "title": "内存使用率", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "current"}, {"cacheTimeout": null, "colorBackground": false, "colorPostfix": false, "colorPrefix": false, "colorValue": true, "colors": ["#299c46", "rgba(237, 129, 40, 0.89)", "#d44a3a"], "datasource": "Prometheus", "decimals": 1, "description": "", "fieldConfig": {"defaults": {}, "overrides": []}, "format": "bytes", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 2, "w": 2, "x": 0, "y": 18}, "id": 37, "interval": "", "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "100%", "prefix": "", "prefixFontSize": "100%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "repeat": "host", "repeatDirection": "v", "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "tableColumn": "windows_cs_physical_memory_bytes{instance=\"*************:9182\", job=\"windows-exporter\"}", "targets": [{"expr": "windows_cs_physical_memory_bytes{job=~\"$job\",instance=~\"$instance\"}", "format": "time_series", "instant": false, "interval": "", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "thresholds": "", "timeFrom": null, "timeShift": null, "title": "总内存", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "current"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "decimals": 1, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 20}, "hiddenSeries": false, "id": 25, "legend": {"alignAsTable": false, "avg": true, "current": true, "max": true, "min": true, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "paceLength": 10, "percentage": false, "pluginVersion": "7.5.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "100 - avg(irate(windows_cpu_time_total{job=~\"$job\",instance=~\"$instance\",mode=\"idle\"}[5m]))*100", "hide": false, "interval": "", "legendFormat": "CPU使用率", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "CPU使用率", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:366", "format": "percent", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:367", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {"Free Physical memory": "semi-dark-green", "Free physical memory": "semi-dark-green", "Free virtual memory": "yellow", "Physical memory": "dark-red", "Virtual memory": "dark-purple", "剩余物理内存": "green", "总物理内存": "dark-red"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "decimals": 2, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 3, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 20}, "hiddenSeries": false, "hideTimeOverride": false, "id": 14, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 0, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:225", "alias": "/.*物理内存.*/", "linewidth": 2}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": ""}, "expr": "windows_cs_physical_memory_bytes{job=~\"$job\",instance=~\"$instance\"}", "format": "time_series", "functions": [], "group": {"filter": ""}, "hide": false, "host": {"filter": ""}, "interval": "", "intervalFactor": 1, "item": {"filter": ""}, "legendFormat": "总物理内存", "metric": "mysql_global_status_questions", "mode": 0, "options": {"showDisabledItems": false}, "refId": "B", "step": 5}, {"application": {"filter": ""}, "expr": "windows_os_physical_memory_free_bytes{job=~\"$job\",instance=~\"$instance\"}", "format": "time_series", "functions": [], "group": {"filter": ""}, "hide": false, "host": {"filter": ""}, "interval": "", "intervalFactor": 1, "item": {"filter": ""}, "legendFormat": "剩余物理内存", "metric": "mysql_global_status_questions", "mode": 0, "options": {"showDisabledItems": false}, "refId": "C", "step": 5}, {"application": {"filter": ""}, "expr": "windows_os_virtual_memory_bytes{job=~\"$job\",instance=~\"$instance\"}", "format": "time_series", "functions": [], "group": {"filter": ""}, "hide": false, "host": {"filter": ""}, "intervalFactor": 1, "item": {"filter": ""}, "legendFormat": "Virtual memory", "metric": "mysql_global_status_questions", "mode": 0, "options": {"showDisabledItems": false}, "refId": "A", "step": 5}, {"expr": "windows_os_virtual_memory_free_bytes{job=~\"$job\",instance=~\"$instance\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Free virtual memory", "refId": "D"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "内存详情", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:197", "format": "bytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:198", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 20}, "hiddenSeries": false, "hideTimeOverride": false, "id": 15, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:7159", "alias": "/总空间.*/", "color": "#F2495C"}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": ""}, "expr": "windows_logical_disk_free_bytes{job=~\"$job\",instance=~\"$instance\"}", "format": "time_series", "functions": [], "group": {"filter": ""}, "hide": false, "host": {"filter": ""}, "interval": "", "intervalFactor": 1, "item": {"filter": ""}, "legendFormat": "剩余空间 {{volume}}", "metric": "mysql_global_status_questions", "mode": 0, "options": {"showDisabledItems": false}, "refId": "A", "step": 20}, {"application": {"filter": ""}, "expr": "windows_logical_disk_size_bytes{job=~\"$job\",instance=~\"$instance\"}", "format": "time_series", "functions": [], "group": {"filter": ""}, "hide": false, "host": {"filter": ""}, "interval": "", "intervalFactor": 1, "item": {"filter": ""}, "legendFormat": "总空间 {{volume}}", "metric": "mysql_global_status_questions", "mode": 0, "options": {"showDisabledItems": false}, "refId": "B", "step": 20}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "磁盘剩余空间", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:312", "format": "decbytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:313", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 28}, "hiddenSeries": false, "hideTimeOverride": false, "id": 11, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": false, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": ""}, "expr": "irate(windows_net_bytes_sent_total{job=~\"$job\",instance=~\"$instance\",nic!~'isatap.*|VPN.*'}[5m])*8>0\r", "format": "time_series", "functions": [], "group": {"filter": ""}, "hide": false, "host": {"filter": ""}, "interval": "", "intervalFactor": 1, "item": {"filter": ""}, "legendFormat": "{{nic}}-Sent-上传", "metric": "mysql_global_status_questions", "mode": 0, "options": {"showDisabledItems": false}, "refId": "B", "step": 10}, {"application": {"filter": ""}, "expr": "irate(windows_net_bytes_received_total{job=~\"$job\",instance=~\"$instance\",nic!~'isatap.*|VPN.*'}[5m])*8\n", "format": "time_series", "functions": [], "group": {"filter": ""}, "hide": false, "host": {"filter": ""}, "intervalFactor": 1, "item": {"filter": ""}, "legendFormat": "{{nic}}-Received-下载", "metric": "mysql_global_status_questions", "mode": 0, "options": {"showDisabledItems": false}, "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "网络详情", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:137", "format": "bps", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:138", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 2, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 28}, "hiddenSeries": false, "hideTimeOverride": false, "id": 8, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": false, "show": true, "sort": null, "sortDesc": null, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": ""}, "expr": "irate(windows_logical_disk_read_bytes_total{job=~\"$job\",instance=~\"$instance\"}[5m])\r", "format": "time_series", "functions": [], "group": {"filter": ""}, "hide": false, "host": {"filter": ""}, "intervalFactor": 1, "item": {"filter": ""}, "legendFormat": "read {{volume}}", "metric": "mysql_global_status_questions", "mode": 0, "options": {"showDisabledItems": false}, "refId": "A", "step": 20}, {"application": {"filter": ""}, "expr": "irate(windows_logical_disk_write_bytes_total{job=~\"$job\",instance=~\"$instance\"}[5m])\r", "format": "time_series", "functions": [], "group": {"filter": ""}, "hide": false, "host": {"filter": ""}, "intervalFactor": 1, "item": {"filter": ""}, "legendFormat": "write {{volume}}", "metric": "mysql_global_status_questions", "mode": 0, "options": {"showDisabledItems": false}, "refId": "B", "step": 20}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "磁盘读写", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:761", "format": "Bps", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:762", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 28}, "hiddenSeries": false, "hideTimeOverride": false, "id": 9, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": ""}, "expr": "irate(windows_logical_disk_reads_total{job=~\"$job\",instance=~\"$instance\"}[5m])", "format": "time_series", "functions": [], "group": {"filter": ""}, "hide": false, "host": {"filter": ""}, "intervalFactor": 1, "item": {"filter": ""}, "legendFormat": "read {{volume}}", "metric": "mysql_global_status_questions", "mode": 0, "options": {"showDisabledItems": false}, "refId": "B", "step": 20}, {"application": {"filter": ""}, "expr": "irate(windows_logical_disk_writes_total{job=~\"$job\",instance=~\"$instance\"}[5m])", "format": "time_series", "functions": [], "group": {"filter": ""}, "hide": false, "host": {"filter": ""}, "intervalFactor": 1, "item": {"filter": ""}, "legendFormat": "write {{volume}}", "metric": "mysql_global_status_questions", "mode": 0, "options": {"showDisabledItems": false}, "refId": "C", "step": 20}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "磁盘IO", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:815", "format": "iops", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:816", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "decimals": 2, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 6, "x": 0, "y": 36}, "hiddenSeries": false, "hideTimeOverride": false, "id": 29, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": true, "min": true, "rightSide": false, "show": false, "total": false, "values": true}, "lines": false, "linewidth": 1, "links": [], "nullPointMode": "null", "paceLength": 10, "percentage": false, "pluginVersion": "7.5.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "(irate(windows_net_bytes_total{job=~\"$job\",instance=~\"$instance\",nic!~'isatap.*|VPN.*'}[5m]) * 8 / windows_net_current_bandwidth{job=~\"$job\",instance=~\"$instance\",nic!~'isatap.*|VPN.*'}) * 100", "legendFormat": "{{nic}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "网络使用率", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:915", "format": "percent", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:916", "format": "bytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 6, "x": 6, "y": 36}, "hiddenSeries": false, "hideTimeOverride": false, "id": 10, "legend": {"alignAsTable": false, "avg": false, "current": true, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pluginVersion": "7.5.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": ""}, "expr": "irate(windows_net_packets_outbound_discarded{job=~\"$job\",instance=~\"$instance\", nic!~'isatap.*|VPN.*'}[5m]) + irate(windows_net_packets_outbound_errors{job=~\"$job\",instance=~\"$instance\"}[5m])", "format": "time_series", "functions": [], "group": {"filter": ""}, "hide": false, "host": {"filter": ""}, "interval": "", "intervalFactor": 1, "item": {"filter": ""}, "legendFormat": "outbound", "metric": "mysql_global_status_questions", "mode": 0, "options": {"showDisabledItems": false}, "refId": "B", "step": 15}, {"application": {"filter": ""}, "expr": "irate(windows_net_packets_received_discarded{job=~\"$job\",instance=~\"$instance\", nic!~'isatap.*|VPN.*'}[5m]) + irate(windows_net_packets_received_errors{job=~\"$job\",instance=~\"$instance\"}[5m])", "format": "time_series", "functions": [], "group": {"filter": ""}, "hide": false, "host": {"filter": ""}, "interval": "", "intervalFactor": 1, "item": {"filter": ""}, "legendFormat": "received", "metric": "mysql_global_status_questions", "mode": 0, "options": {"showDisabledItems": false}, "refId": "A", "step": 15}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Network discarded/error packets", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:518", "format": "pps", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:519", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 6, "x": 12, "y": 36}, "hiddenSeries": false, "hideTimeOverride": false, "id": 12, "legend": {"alignAsTable": false, "avg": false, "current": true, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pluginVersion": "7.5.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": ""}, "expr": "windows_system_threads{job=~\"$job\",instance=~\"$instance\"}", "format": "time_series", "functions": [], "group": {"filter": ""}, "hide": false, "host": {"filter": ""}, "interval": "", "intervalFactor": 1, "item": {"filter": ""}, "legendFormat": "system_threads", "metric": "mysql_global_status_questions", "mode": 0, "options": {"showDisabledItems": false}, "refId": "A", "step": 20}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "system_threads", "tooltip": {"shared": false, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:414", "format": "none", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:415", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 6, "x": 18, "y": 36}, "hiddenSeries": false, "hideTimeOverride": false, "id": 13, "legend": {"alignAsTable": false, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pluginVersion": "7.5.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": ""}, "expr": "irate(windows_system_exception_dispatches_total{job=~\"$job\",instance=~\"$instance\"}[5m])", "format": "time_series", "functions": [], "group": {"filter": ""}, "hide": false, "host": {"filter": ""}, "interval": "", "intervalFactor": 1, "item": {"filter": ""}, "legendFormat": "exceptions", "metric": "mysql_global_status_questions", "mode": 0, "options": {"showDisabledItems": false}, "refId": "A", "step": 20}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "System exception dispatches", "tooltip": {"shared": false, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:466", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:467", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "refresh": false, "schemaVersion": 27, "style": "dark", "tags": ["Prometheus", "windows_exporter"], "templating": {"list": [{"allValue": null, "current": {"isNone": true, "selected": false, "text": "None", "value": ""}, "datasource": "Prometheus", "definition": "label_values(windows_cs_hostname, job)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "JOB", "multi": false, "name": "job", "options": [], "query": {"query": "label_values(windows_cs_hostname, job)", "refId": "Prometheus-job-Variable-Query"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": "Prometheus", "definition": "label_values(windows_cs_hostname{job=~\"$job\"}, hostname)", "description": null, "error": null, "hide": 0, "includeAll": true, "label": "主机名", "multi": false, "name": "hostname", "options": [], "query": {"query": "label_values(windows_cs_hostname{job=~\"$job\"}, hostname)", "refId": "Prometheus-hostname-Variable-Query"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 5, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"isNone": true, "selected": false, "text": "None", "value": ""}, "datasource": "Prometheus", "definition": "label_values(windows_cs_hostname{job=~\"$job\",hostname=~\"$hostname\"}, instance)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "instance", "multi": false, "name": "instance", "options": [], "query": {"query": "label_values(windows_cs_hostname{job=~\"$job\",hostname=~\"$hostname\"}, instance)", "refId": "Prometheus-instance-Variable-Query"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 5, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"isNone": true, "selected": false, "text": "None", "value": ""}, "datasource": "Prometheus", "definition": "label_values(windows_cs_hostname{job=~\"$job\",instance=~\"$instance\"}, hostname)", "description": null, "error": null, "hide": 2, "includeAll": false, "label": "展示使用的主机名", "multi": false, "name": "show_hostname", "options": [], "query": {"query": "label_values(windows_cs_hostname{job=~\"$job\",instance=~\"$instance\"}, hostname)", "refId": "Prometheus-show_hostname-Variable-Query"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-3h", "to": "now"}, "timepicker": {"refresh_intervals": ["10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "", "rows": [{}], "title": "1   windows_exporter for Prometheus Dashboard CN v20201012", "uid": "Kdh0OoSGz", "version": 0}, "overwrite": false}