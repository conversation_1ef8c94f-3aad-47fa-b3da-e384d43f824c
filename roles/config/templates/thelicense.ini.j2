[app]
PageSize = 10
JwtSecret = 233
PrefixUrl = http://{{ vip }}:8059

RuntimeRootPath = runtime/

ImageSavePath = upload/images/
# MB
ImageMaxSize = 5
ImageAllowExts = .jpg,.jpeg,.png

ExportSavePath = export/
QrCodeSavePath = qrcode/
FontSavePath = fonts/

LogSavePath = logs/
LogSaveName = log
LogFileExt = log
TimeFormat = 20060102

[server]
#debug or release
RunMode = debug
HttpPort = 8059
ReadTimeout = 60
WriteTimeout = 60

[ceph]
Url = http://{{ vip }}:8443
Path = /api/auth
Username = admin
Password = p@ssw0rd

[prometheus]
Url = http://{{ vip }}:9090

; [database]
; Type = mysql
; User = root
; Password = rootroot
; Host = 127.0.0.1:3306
; Name = blog
; TablePrefix = blog_
;
; [redis]
; Host = 127.0.0.1:6379
; Password =
; MaxIdle = 30
; MaxActive = 30
; IdleTimeout = 200