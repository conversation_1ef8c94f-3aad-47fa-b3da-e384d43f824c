version: "3.7"


configs:
  traefik_router.yml:
    file: /etc/thestack/conf/traefik/tcp_routers.yml #未使用
  # mariadb_init.sql:
  #   file: /etc/thestack/conf/mariadb/init.sql
  redis.conf:
    file: /etc/thestack/conf/redis/redis.conf #未使用
  # fluent.conf:
  #   file: /etc/thestack/conf/efk/fluent.conf
  # theauth.ini:
  #   file: /etc/thestack/conf/theauth/config.ini



#desk
  # thedesk.py:
  #   file: /etc/thestack/conf/theweb/settings.py
  # thedeskapi.ini:
  #   file: /etc/thestack/conf/thedeskapi/config.ini
    
#healthcheck
  # thehealthcheck.py:
  #   file: /etc/thestack/conf/theweb/settings.py

#celery
  # celery_beat.py:
  #   file: /etc/thestack/conf/theweb/settings.py
  # celery_worker.py:
  #   file: /etc/thestack/conf/theweb/settings.py
  # celery_migrate_beat.py:
  #   file: /etc/thestack/conf/theweb/settings.py
  # celery_migrate.py:
  #   file: /etc/thestack/conf/theweb/settings.py


# #dashbord
#   theweb.py:
#     file: /etc/thestack/conf/theweb/settings.py
#   theupload.py:
#     file: /etc/thestack/conf/theweb/settings.py
#   thevmware.py:
#     file: /etc/thestack/conf/theweb/settings.py
#   theuser.py:
#     file: /etc/thestack/conf/theweb/settings.py
#   thelog.py:
#     file: /etc/thestack/conf/theweb/settings.py
#   theac.py:
#     file: /etc/thestack/conf/theweb/settings.py

#thedb-bootstrap
  # thedb-bootstrap.py:
  #   file: /etc/thestack/conf/theweb/settings.py

#vip
  # keepalived.conf:
  #   file: /etc/thestack/conf/keeplived/keeplived.conf
  # haproxy.cfg:
  #   file: /etc/thestack/conf/haproxy/haproxy.cfg

#thecephapi
  # thecephapi.yml:
  #   file: /etc/thestack/conf/thecephapi/thecephapi.yml
  # thecss.properties:
  #   file: /etc/thestack/conf/thecephapi/application.properties
#storageapi
  # storageapi.ini:
  #   file: /etc/thestack/conf/storageapi/storageapi.ini