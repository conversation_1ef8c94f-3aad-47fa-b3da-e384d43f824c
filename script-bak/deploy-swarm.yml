---
- name: setup docker pre-requisites
  hosts: swarm-managers
  become: true
  roles:
    - sys-modules

#- name: setup ntp server
#  hosts: admin
#  become: yes
#  become_user: root
#  gather_facts: yes
#  become_method: sudo
#  roles:
#    - ntp

- name: setup chrony
  hosts: swarm-managers
  roles:
    - chrony

- name: setup repo
  hosts: swarm-managers
  roles:
    - repo
    
- name: setup pre-requisites
  hosts: swarm-managers
  roles:
    - create-sudo-user
    - install-modules
#    - configure-hosts-file

#- name: setup docker user on the nodes
#  become: yes
#  become_user: docker
#  hosts: swarm-managers
#  roles:
#    - configure-admin

- name: copy public key to nodes
  become: yes
  become_user: docker
  hosts: swarm-managers
  roles:
    - copy-keys

#- name: install docker
#  hosts: docker-nodes
#  roles:
#    - docker-installation

- name: initialize docker swarm
  hosts: swarm-managers
  roles:
    - docker-swarm-init

- name: add workers to the swarm
  hosts: swarm-workers
  roles:
    - docker-swarm-add-worker
