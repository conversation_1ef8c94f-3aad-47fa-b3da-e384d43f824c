version: "3.7"

networks:
  traefik_prod:
    external: true

services:
  thedb-bootstrap:
    image: tianwen1:5000/thedb-bootstrap:latest
    hostname: bootstrap
    networks:
      - traefik_prod
    configs:
      - source: thedb-bootstrap.py
        target: /code/settings.py
    deploy:
      replicas: 1
      placement:
        constraints:
          - node.role==manager
      restart_policy:
        condition: on-failure
        delay: 3s
        max_attempts: 5
        window: 60s

configs:
  thedb-bootstrap.py:
    file: /etc/thestack/conf/theweb/settings.py