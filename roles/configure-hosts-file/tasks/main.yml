---
#- name: set hostname
#  shell: hostnamectl set-hostname   "{{ inventory_hostname }}"
#  when: set_hostname
  
- name: Configure Hosts File
  lineinfile: path=/etc/hosts regexp='.*{{ item }}$' line="{{ hostvars[item].ansible_host }} {{item}}" state=present
  when: hostvars[item].ansible_default_ipv4.address is defined and set_hostname
  with_items: "{{ groups['docker'] }}"
  
#- name: Configure Hosts File
#  lineinfile: path=/etc/hosts regexp='.*tianwen$' line="{{ tianwen1 }} tianwen1" state=present
#  with_items: "{{ groups['docker'] }}"