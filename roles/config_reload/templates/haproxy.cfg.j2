global
    log stdout local0
    maxconn 4096

defaults
    log global
    mode http
    timeout connect 5000ms
    timeout client 50000ms
    timeout server 50000ms

listen status
    bind {{ vip }}:8932
    mode http
    stats enable
    stats uri /
    stats refresh 15s
    stats realm Haproxy\ Stats
    stats auth admin:admin

listen cephapi
    mode http
    bind {{ vip }}:8443
    capture cookie vgnvisitor= len 32
    cookie SERVERID insert indirect nocache
    option forwardfor
    mode http
    option forwardfor
    option httpchk  GET /
    http-check expect status 200
    option httpclose
    {% for host in  groups.get('docker-nodes')  %}
server {{ host }} {{ hostvars[host]['ansible_host'] }}:8443 cookie {{ host }} check inter 2000 rise 2 fall 5
    {% endfor %}



listen nova_novncproxy
    bind {{ vip }}:6080
    balance source
    option tcpka
    option tcplog
    {% for host in  groups.get('docker-nodes')  %}
server {{ host }} {{ hostvars[host]['ansible_host'] }}:6080 check inter 2000 rise 2 fall 5
    {% endfor %}



