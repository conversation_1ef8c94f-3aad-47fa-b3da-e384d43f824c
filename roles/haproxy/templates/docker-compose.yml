version: "3.7"
services:
  haproxy:
    hostname: haproxy
    image: tianwen1:5000/haproxy:2.4
    ulimits:
      nofile:
        soft: 65536
        hard: 65536
    configs:
      - source: haproxy.cfg
        target: /usr/local/etc/haproxy/haproxy.cfg
    networks:
      - hostnet
    deploy:
      mode: global
      placement:
        constraints:
          - node.role == manager


networks:
  hostnet:
    external: true
    name: host

configs:
  haproxy.cfg:
    file: /etc/thestack/conf/haproxy/haproxy.cfg

    