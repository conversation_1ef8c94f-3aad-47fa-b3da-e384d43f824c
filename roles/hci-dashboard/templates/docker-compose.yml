version: "3.7"
services:
  hci_webvue:
    image: tianwen1:5000/hci_vue:latest
    #network_mode: "service:traefik_reverse-proxy"
    networks:
      - prod
    configs:
      - source: thewebvue.config.js
        target: /code/dist/static/config.js
      - source: thewebvue.serialconfig.js
        target: /code/dist/static/serialconfig.js
    deploy:
      replicas: 1
      labels:
        - traefik.enable=true
        - traefik.http.middlewares.thewebvue_strip.stripprefix.prefixes=/
        - traefik.http.routers.thewebvue.rule=PathPrefix(`/`)
        - traefik.http.routers.thewebvue.entrypoints=web,websecure
        - traefik.http.services.thewebvue.loadbalancer.server.port=8066
        - traefik.http.routers.thewebvue.middlewares=thewebvue_strip
        - traefik.http.routers.thewebvue.tls=true
        - traefik.docker.network=prod

  theauth:
    image: tianwen1:5000/hci_auth:latest
    # network_mode: "service:traefik_reverse-proxy"
    networks:
      - prod
    volumes:
      - /var/log/the:/var/log/the
      - /usr/share/zoneinfo/Asia/Shanghai:/etc/localtime
    configs:
      - source: theauth.ini
        target: /etc/vdiauth/config.ini
    deploy:
      replicas: 1
      labels:
        - traefik.enable=true
        - traefik.http.middlewares.theauth_strip.stripprefix.prefixes=/login
        - traefik.http.routers.theauth.rule=PathPrefix(`/login`)
        - traefik.http.routers.theauth.entrypoints=web,websecure
        - traefik.http.services.theauth.loadbalancer.server.port=8088
        - traefik.http.routers.theauth.middlewares=theauth_strip
        - traefik.http.routers.theauth.tls=true
        - traefik.docker.network=prod

  theweb:
    image: tianwen1:5000/hci_web:latest
    hostname: theweb
    # network_mode: "service:traefik_reverse-proxy"
    networks:
      - prod
    volumes:
      - /var/log/the:/var/log/the
      - /usr/share/zoneinfo/Asia/Shanghai:/etc/localtime
    configs:
      - source: theweb.py
        target: /code/settings.py
      - source: theweb.py
        target: /hci_asyn/config/settings.py
    deploy:
      replicas: 1
      labels:
        - traefik.enable=true
        - traefik.http.middlewares.theweb_strip.stripprefix.prefixes=/theapi
        - traefik.http.routers.theweb.rule=PathPrefix(`/theapi`)
        - traefik.http.routers.theweb.entrypoints=web,websecure
        - traefik.http.services.theweb.loadbalancer.server.port=8006
        # - traefik.http.middlewares.theweb-auth.forwardauth.address=http://acapi:9998/v1/auth/
        # - traefik.http.routers.theweb.middlewares=theweb_strip,theweb-auth
        - traefik.http.routers.theweb.middlewares=theweb_strip
        - traefik.http.routers.theweb.tls=true
        - traefik.docker.network=prod

  hci_message:
    container_name: hci_message
    image: tianwen1:5000/hci_sc:latest
    networks:
      - prod
    command: ["python3", "-u", "/usr/local/bin/nameko", "run", "--config", "/etc/dayu/message.yml", "dayu.message.service"]
    configs:
      - source: message.yml
        target: /etc/dayu/message.yml
      # - source: message.py
      #   target: /thesc/settings.py
    deploy:
      replicas: 1
      placement:
        constraints:
          - node.role==manager
      labels:
        - traefik.enable=true
        - traefik.docker.network=prod
        - traefik.tcp.routers.message.rule=HostSNI(`*`)
        - traefik.tcp.routers.message.entrypoints=message
        - traefik.tcp.routers.message.tls=false
        - traefik.tcp.routers.message.service=scheduler
        - traefik.tcp.services.message.loadbalancer.server.port=9192

  thewebsocket:
    image: tianwen1:5000/thewebsocket:latest
    hostname: thewebsocket
    networks:
      - prod
    volumes:
      - /usr/share/zoneinfo/Asia/Shanghai:/etc/localtime
      - /root/.ssh:/root/.ssh
    deploy:
      replicas: 1
      labels:
        - traefik.enable=true
        - traefik.http.middlewares.thewebsocket_strip.stripprefix.prefixes=/thewebsocket
        - traefik.http.routers.thewebsocket.rule=PathPrefix(`/thewebsocket`)
        - traefik.http.routers.thewebsocket.entrypoints=web,websecure
        - traefik.http.services.thewebsocket.loadbalancer.server.port=9180
        - traefik.http.routers.thewebsocket.middlewares=thewebsocket_strip
        - traefik.http.routers.thewebsocket.tls=true
        - traefik.docker.network=prod

  theupload:
    image: tianwen1:5000/hci_upload:latest
    hostname: theupload
    networks:
      - prod
    volumes:
      - /etc/ceph:/etc/ceph
      - theshare:/data/share
      - /var/log/the:/var/log/the
      - /usr/share/zoneinfo/Asia/Shanghai:/etc/localtime
    configs:
      - source: theupload.py
        target: /code/settings.py
    deploy:
      replicas: 1
      labels:
        - traefik.enable=true
        - traefik.http.middlewares.theupload_strip.stripprefix.prefixes=/upload
        - traefik.http.routers.theupload.rule=PathPrefix(`/upload`)
        - traefik.http.routers.theupload.entrypoints=web,websecure
        - traefik.http.services.theupload.loadbalancer.server.port=8090
        - traefik.http.routers.theupload.middlewares=theupload_strip
        - traefik.http.routers.theupload.tls=true
        - traefik.docker.network=prod

  thevmware:
    image: tianwen1:5000/thevmware:latest
    hostname: thevmware
    networks:
      - prod
    volumes:
      - /var/log/the:/var/log/the
      - /usr/share/zoneinfo/Asia/Shanghai:/etc/localtime
    configs:
      - source: thevmware.py
        target: /code/settings.py
    deploy:
      replicas: 1
      labels:
        - traefik.enable=true
        - traefik.http.middlewares.thevmware_strip.stripprefix.prefixes=/cloudy
        - traefik.http.routers.thevmware.rule=PathPrefix(`/cloudy`)
        - traefik.http.routers.thevmware.entrypoints=web,websecure
        - traefik.http.services.thevmware.loadbalancer.server.port=8091
        - traefik.http.routers.thevmware.middlewares=thevmware_strip
        - traefik.http.routers.thevmware.tls=true
        - traefik.docker.network=prod

  theuser:
    image: tianwen1:5000/hci_user:latest
    hostname: theuser
    networks:
      - prod
    volumes:
      - /var/log/the:/var/log/the
      - /usr/share/zoneinfo/Asia/Shanghai:/etc/localtime
    configs:
      - source: theuser.py
        target: /code/settings.py
    deploy:
      replicas: 1
      labels:
        - traefik.enable=true
        - traefik.http.middlewares.theuser_strip.stripprefix.prefixes=/user
        - traefik.http.routers.theuser.rule=PathPrefix(`/user`)
        - traefik.http.routers.theuser.entrypoints=web,websecure
        - traefik.http.services.theuser.loadbalancer.server.port=8089
        - traefik.http.routers.theuser.middlewares=theuser_strip
        - traefik.http.routers.theuser.tls=true
        - traefik.docker.network=prod


  hci_asyn:
    container_name: hci_asyn
    image: tianwen1:5000/hci_asyn:latest
    hostname: hci_asyn
    command: celery -A app.celery worker --loglevel=info --concurrency=1 --queues="hci_web"
    networks:
      - prod
    configs:
      - source: celery_worker.py
        target: /code/config/settings.py
    volumes:
      - theshare:/data/share
      - /var/log/the:/var/log/the
      - /usr/share/zoneinfo/Asia/Shanghai:/etc/localtime
    deploy:
      replicas: 1
      placement:
        constraints:
          - node.role==manager




  mgr:
    image: tianwen1:5000/hci_mgr:latest
    hostname: mgr
    networks:
      - prod
    volumes:
      #- /var/log/the:/var/log/the
      - /usr/share/zoneinfo/Asia/Shanghai:/etc/localtime
    configs:
      - source: theweb.py
        target: /code/settings.py
    deploy:
      mode: replicated
      replicas: 1
      restart_policy:
        condition: any
        delay: 10s
        max_attempts: 3
        window: 180s
      placement:
        constraints:
          - node.role==manager
      labels:
        - traefik.enable=true
        - traefik.docker.network=traefik_prod
        - traefik.tcp.routers.mgr.rule=HostSNI(`*`)
        - traefik.tcp.routers.mgr.entrypoints=mgr
        - traefik.tcp.routers.mgr.tls=false
        - traefik.tcp.routers.mgr.service=mgr
        - traefik.tcp.services.mgr.loadbalancer.server.port=8886


volumes:
  theshare:

networks:
  prod:
    name: prod
    external: true

configs:
  theweb.py:
    file: /etc/thestack/conf/hci/settings.py
  celery_worker.py:
    file: /etc/thestack/conf/hci/settings.py
  theupload.py:
    file: /etc/thestack/conf/hci/settings.py
  thevmware.py:
    file: /etc/thestack/conf/hci/settings.py
  theuser.py:
    file: /etc/thestack/conf/hci/settings.py
  thelog.py:
    file: /etc/thestack/conf/hci/settings.py
  theac.py:
    file: /etc/thestack/conf/hci/settings.py
  # message.py:
  #   file: /etc/thestack/conf/message/message.py
  message.yml:
    file: /etc/thestack/conf/message/message.yml
  theauth.ini:
    file: /etc/thestack/conf/auth/config.ini
  thewebvue.config.js:
    file: /etc/thestack/conf/webvue/config.js
  thewebvue.serialconfig.js:
    file: /etc/thestack/conf/webvue/serialconfig.js
