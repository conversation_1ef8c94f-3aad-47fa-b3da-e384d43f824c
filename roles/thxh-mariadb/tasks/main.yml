---
- name: Upload docker compose file
  template:
    src: "{{ src_docker_compose }}"
    dest: "{{ dest_docker_compose }}"

- name: Copy db.env to device
  copy:
    src: "{{ src_db_env }}"
    dest: "{{ dest_db_env }}"

- name: Deploy services
  shell: docker stack deploy -c {{ dest_docker_compose }} {{ stack_name }}

- name: Pause for 10 seconds to wait for services being running
  pause:
    seconds: 10

- name: Ensure services deployed
  shell: docker service ls
  register: service_output

- name: Output service list
  debug:
    msg: "{{ service_output.stdout_lines }}"

- name: Show stacks
  shell: docker stack list
