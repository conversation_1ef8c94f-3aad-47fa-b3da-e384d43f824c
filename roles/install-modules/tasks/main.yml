---
- name: remove podman runc
  dnf:
    name:
      - podman
      - runc
    state: removed
  when: install_package    

- name: Install Packages
  dnf:
    name:  
      - docker-ce
    state: latest
  when: install_package


- name: restart docker
  service:
    name: docker
    state: started
    enabled: yes
  when: install_package

- name: config docker daemaon
  template:
    src: "daemon.json"
    dest: "/etc/docker/daemon.json"
  when: install_package

- name: restart docker
  service:
    name: docker
    state: restarted
    enabled: yes
  when: install_package 

  
- name: pip3 install Packages
  shell: "pip3 install {{ item }}"
  when: install_package
  with_items:
     - requests
     - docker
  
  
