---
- name: deploy keeplived
  hosts: managers
  vars:
    src_docker_compose: docker-compose.yml.j2
    dest_docker_compose: /etc/thestack/compose/docker-compose-keeplived.yml

    src_keeplived_master_conf: keeplived-master.conf.j2
    dest_keeplived_master_conf: /etc/thestack/conf/keeplived/keeplived-master.conf

    src_keeplived_backup1_conf: keeplived-backup1.conf.j2
    dest_keeplived_backup1_conf: /etc/thestack/conf/keeplived/keeplived-backup1.conf
    
    src_keeplived_backup2_conf: keeplived-backup2.conf.j2
    dest_keeplived_backup2_conf: /etc/thestack/conf/keeplived/keeplived-backup2.conf
       
    stack_name: keeplived
    is_remove: "{{ remove }}"
  tasks:

    - name: Print install_status
      debug:
        msg: "{{ is_remove }}"

    - name: install keeplived
      include_role:
        name: keeplived
      when:  is_remove == 'false'

    - name: uninstall keeplived
      shell: docker stack rm keeplived
      when: "is_remove == 'true'"
      run_once: true  
