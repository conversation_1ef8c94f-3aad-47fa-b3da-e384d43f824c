---
- name: deploy thelicense
  hosts: managers
  vars:
    src_docker_compose: docker-compose.yml
    dest_docker_compose: /etc/thestack/compose/docker-compose-thelicense.yml
    stack_name: thelicense
    is_remove: "{{ remove }}"
  tasks:

    - name: Print install_status
      debug:
        msg: "{{ is_remove }}"

    - name: install thelicense
      include_role:
        name: thelicense
      when: is_remove == 'false'

    - name: uninstall thelicense
      shell: docker stack rm thelicense
      when: is_remove == 'true'
      run_once: true  
