import os

# Redis 配置
MQ_TYPE="redis"
REDIS_URL = "redis://{{ vip }}:6379/0"
AMQP_URI = "amqp://admin:admin123@{{ vip }}//"
RPC_URI = "rpc://admin:admin123@{{ vip }}//"

MSG_AMQP_URI = "pyamqp://admin:admin123@{{ vip }}"

# Celery 配置
CELERY_BROKER_URL = REDIS_URL
CELERY_RESULT_BACKEND = REDIS_URL

# 其他配置
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

DRIVER = "mysql"
DB_USERNAME = "root"
DB_PASSWORD = "thecloud2015.1"
DB_HOSTNAME = "{{ vip }}"
DB_PORT = "3306"
DB_DATABASE = "hci_db"
