serve:
  proxy:
    port: 4455 # run the proxy at port 4455
  api:
    port: 4456 # run the api at port 4456

access_rules:
  repositories:
    - file:///rules.json

errors:
  fallback:
    - json
  handlers:
    json:
      enabled: true
      config:
        verbose: true
    redirect:
      enabled: true
      config:
        to: https://www.ory.sh/docs

mutators:
  header:
    enabled: true
    config:
      headers:
        X-User: "{{ print .Subject }}"
        # You could add some other headers, for example with data from the
        # session.
        # X-Some-Arbitrary-Data: "{{ print .Extra.some.arbitrary.data }}"
  noop:
    enabled: true
  id_token:
    enabled: true
    config:
      issuer_url: http://oathkeeper:4455/
      jwks_url: file:///jwks.json

authorizers:
  allow:
    enabled: true
  deny:
    enabled: true

authenticators:
  cookie_session:
    enabled: true
    config:
      check_session_url: http://whoami:8088/sessions/whoami
      only:
        - sessionid
      preserve_path: true
      preserve_query: true      
  