version: "3.7"
services:
  thewebvue:
    image: tianwen1:5000/thewebvue:latest
    networks:
      - traefik_prod
    configs:
      - source: thewebvue.config.js
        target: /code/dist/static/config.js
      - source: thewebvue.serialconfig.js
        target: /code/dist/static/serialconfig.js
    deploy:
      replicas: 2
      labels:
        - traefik.enable=true
        - traefik.http.middlewares.thewebvue_strip.stripprefix.prefixes=/
        - traefik.http.routers.thewebvue.rule=PathPrefix(`/`)
        - traefik.http.routers.thewebvue.entrypoints=web,websecure
        - traefik.http.services.thewebvue.loadbalancer.server.port=8066
        - traefik.http.routers.thewebvue.middlewares=thewebvue_strip
        - traefik.http.routers.thewebvue.tls=true
        - traefik.docker.network=traefik_prod

  theauth:
    image: tianwen1:5000/theauth:latest
    networks:
      - traefik_prod
    volumes:
      - /var/log/the:/var/log/the
      - /usr/share/zoneinfo/Asia/Shanghai:/etc/localtime
    configs:
      - source: theauth.ini
        target: /etc/vdiauth/config.ini
    deploy:
      replicas: 1
      labels:
        - traefik.enable=true
        - traefik.http.middlewares.theauth_strip.stripprefix.prefixes=/login
        - traefik.http.routers.theauth.rule=PathPrefix(`/login`)
        - traefik.http.routers.theauth.entrypoints=web,websecure
        - traefik.http.services.theauth.loadbalancer.server.port=8088
        - traefik.http.routers.theauth.middlewares=theauth_strip
        - traefik.http.routers.theauth.tls=true
        - traefik.docker.network=traefik_prod

  theweb:
    image: tianwen1:5000/theweb:latest
    hostname: theweb
    networks:
      - traefik_prod
    volumes:
      - /var/log/the:/var/log/the
      - /usr/share/zoneinfo/Asia/Shanghai:/etc/localtime
    configs:
      - source: theweb.py
        target: /code/settings.py
    deploy:
      replicas: 1
      labels:
        - traefik.enable=true
        - traefik.http.middlewares.theweb_strip.stripprefix.prefixes=/theapi
        - traefik.http.routers.theweb.rule=PathPrefix(`/theapi`)
        - traefik.http.routers.theweb.entrypoints=web,websecure
        - traefik.http.services.theweb.loadbalancer.server.port=8006
        # - traefik.http.middlewares.theweb-auth.forwardauth.address=http://acapi:9998/v1/auth/
        #- traefik.http.routers.theweb.middlewares=theweb_strip,theweb-auth
        - traefik.http.routers.theweb.middlewares=theweb_strip
        - traefik.http.routers.theweb.tls=true
        - traefik.docker.network=traefik_prod

  thecloudapi:
    image: tianwen1:5000/thecloudapi:latest
    hostname: thecloudapi
    configs:
      - source: thecloudapi.ini
        target: /opt/thecloud/conf/app.ini
    networks:
      - traefik_prod
    volumes:
      - /var/log/the:/var/log/the
      - /usr/share/zoneinfo/Asia/Shanghai:/etc/localtime
      - /var/run/docker.sock:/var/run/docker.sock
    deploy:
      replicas: 1
      labels:
        - traefik.enable=true
        - "traefik.http.middlewares.thecloudapi_strip.stripprefix.prefixes=/thecloudapi"
        - traefik.http.routers.thecloudapi.rule=PathPrefix(`/thecloudapi`)
        - traefik.http.routers.thecloudapi.entrypoints=web,websecure
        - traefik.http.services.thecloudapi.loadbalancer.server.port=8007
        - traefik.http.routers.thecloudapi.middlewares=thecloudapi_strip
        - traefik.http.routers.thecloudapi.tls=true
        - traefik.docker.network=traefik_prod

  thewebsocket:
    image: tianwen1:5000/thewebsocket:latest
    hostname: thewebsocket
    networks:
      - traefik_prod
    volumes:
      - /usr/share/zoneinfo/Asia/Shanghai:/etc/localtime
      - /root/.ssh:/root/.ssh
    deploy:
      replicas: 1
      labels:
        - traefik.enable=true
        - traefik.http.middlewares.thewebsocket_strip.stripprefix.prefixes=/thewebsocket
        - traefik.http.routers.thewebsocket.rule=PathPrefix(`/thewebsocket`)
        - traefik.http.routers.thewebsocket.entrypoints=web,websecure
        - traefik.http.services.thewebsocket.loadbalancer.server.port=9180
        - traefik.http.routers.thewebsocket.middlewares=thewebsocket_strip
        - traefik.http.routers.thewebsocket.tls=true
        - traefik.docker.network=traefik_prod

  theupload:
    image: tianwen1:5000/theupload:latest
    hostname: theupload
    networks:
      - traefik_prod
    volumes:
      - /etc/ceph:/etc/ceph
      - theshare:/data/share
      - /var/log/the:/var/log/the
      - /usr/share/zoneinfo/Asia/Shanghai:/etc/localtime
    configs:
      - source: theupload.py
        target: /code/settings.py
    deploy:
      replicas: 1
      labels:
        - traefik.enable=true
        - traefik.http.middlewares.theupload_strip.stripprefix.prefixes=/upload
        - traefik.http.routers.theupload.rule=PathPrefix(`/upload`)
        - traefik.http.routers.theupload.entrypoints=web,websecure
        - traefik.http.services.theupload.loadbalancer.server.port=8090
        - traefik.http.routers.theupload.middlewares=theupload_strip
        - traefik.http.routers.theupload.tls=true
        - traefik.docker.network=traefik_prod

  thevmware:
    image: tianwen1:5000/thevmware:latest
    hostname: thevmware
    networks:
      - traefik_prod
    volumes:
      - /var/log/the:/var/log/the
      - /usr/share/zoneinfo/Asia/Shanghai:/etc/localtime
    configs:
      - source: thevmware.py
        target: /code/settings.py
    deploy:
      replicas: 1
      labels:
        - traefik.enable=true
        - traefik.http.middlewares.thevmware_strip.stripprefix.prefixes=/cloudy
        - traefik.http.routers.thevmware.rule=PathPrefix(`/cloudy`)
        - traefik.http.routers.thevmware.entrypoints=web,websecure
        - traefik.http.services.thevmware.loadbalancer.server.port=8091
        - traefik.http.routers.thevmware.middlewares=thevmware_strip
        - traefik.http.routers.thevmware.tls=true
        - traefik.docker.network=traefik_prod

  theuser:
    image: tianwen1:5000/theuser:latest
    hostname: theuser
    networks:
      - traefik_prod
    volumes:
      - /var/log/the:/var/log/the
      - /usr/share/zoneinfo/Asia/Shanghai:/etc/localtime
    configs:
      - source: theuser.py
        target: /code/settings.py
    deploy:
      replicas: 1
      labels:
        - traefik.enable=true
        - traefik.http.middlewares.theuser_strip.stripprefix.prefixes=/user
        - traefik.http.routers.theuser.rule=PathPrefix(`/user`)
        - traefik.http.routers.theuser.entrypoints=web,websecure
        - traefik.http.services.theuser.loadbalancer.server.port=8089
        - traefik.http.routers.theuser.middlewares=theuser_strip
        - traefik.http.routers.theuser.tls=true
        - traefik.docker.network=traefik_prod



  thelog:
    image: tianwen1:5000/thelog:latest
    hostname: thelog
    networks:
      - traefik_prod
    volumes:
      - /var/log/the:/var/log/the
      - /usr/share/zoneinfo/Asia/Shanghai:/etc/localtime
    configs:
      - source: thelog.py
        target: /code/settings.py
    deploy:
      replicas: 1
      labels:
        - traefik.enable=true
        - traefik.http.middlewares.thelog_strip.stripprefix.prefixes=/thelog
        - traefik.http.routers.thelog.rule=PathPrefix(`/thelog`)
        - traefik.http.routers.thelog.entrypoints=web,websecure
        - traefik.http.services.thelog.loadbalancer.server.port=8093
        - traefik.http.routers.thelog.middlewares=thelog_strip
        - traefik.http.routers.thelog.tls=true
        - traefik.docker.network=traefik_prod

  theac:
    image: tianwen1:5000/theac:latest
    hostname: theac
    networks:
      - traefik_prod
    volumes:
      - /var/log/the:/var/log/the
      - /usr/share/zoneinfo/Asia/Shanghai:/etc/localtime
    configs:
      - source: theac.py
        target: /code/settings.py
    deploy:
      replicas: 1
      labels:
        - traefik.enable=true
        - traefik.http.middlewares.theac_strip.stripprefix.prefixes=/ac
        - traefik.http.routers.theac.rule=PathPrefix(`/ac`)
        - traefik.http.routers.theac.entrypoints=web,websecure
        - traefik.http.services.theac.loadbalancer.server.port=8081
        - traefik.http.routers.theac.middlewares=theac_strip
        - traefik.http.routers.theac.tls=true
        - traefik.docker.network=traefik_prod

  thecloudapi:
    image: tianwen1:5000/thecloudapi:latest
    hostname: thecloudapi
    configs:
      - source: thecloudapi.ini
        target: /opt/thecloud/conf/app.ini
    networks:
      - traefik_prod
    volumes:
      - /var/log/the:/var/log/the
      - /usr/share/zoneinfo/Asia/Shanghai:/etc/localtime
      - /var/run/docker.sock:/var/run/docker.sock
    deploy:
      replicas: 1
      labels:
        - traefik.enable=true
        - "traefik.http.middlewares.thecloudapi_strip.stripprefix.prefixes=/thecloudapi"
        - traefik.http.routers.thecloudapi.rule=PathPrefix(`/thecloudapi`)
        - traefik.http.routers.thecloudapi.entrypoints=web,websecure
        - traefik.http.services.thecloudapi.loadbalancer.server.port=8007
        - traefik.http.routers.thecloudapi.middlewares=thecloudapi_strip
        - traefik.http.routers.thecloudapi.tls=true
        - traefik.docker.network=traefik_prod

volumes:
  theshare:
  
networks:
  traefik_prod:
    external: true

configs:
  theweb.py:
    file: /etc/thestack/conf/theweb/settings.py
  theupload.py:
    file: /etc/thestack/conf/theweb/settings.py
  thevmware.py:
    file: /etc/thestack/conf/theweb/settings.py
  theuser.py:
    file: /etc/thestack/conf/theweb/settings.py
  thelog.py:
    file: /etc/thestack/conf/theweb/settings.py
  theac.py:
    file: /etc/thestack/conf/theweb/settings.py
  theauth.ini:
    file: /etc/thestack/conf/theauth/config.ini
  thewebvue.config.js:
    file: /etc/thestack/conf/thewebvue/config.js
  thewebvue.serialconfig.js:
    file: /etc/thestack/conf/thewebvue/serialconfig.js
  thecloudapi.ini:
    file: /etc/thestack/conf/thecloudapi/app.ini
