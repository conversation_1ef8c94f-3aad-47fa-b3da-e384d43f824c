---
- name: Install chrony
  dnf:
    name: chrony
    state: latest
  ignore_errors: true

- name: Copying over controller1 chrony.conf
  template:
    src: "chrony.conf.c1.j2"
    dest: "/etc/chrony.conf"    
    backup: yes
  when: "inventory_hostname == 'controller1'"  


- name: Copying over controller2 chrony.conf
  template:
    src: "chrony.conf.c2.j2"
    dest: "/etc/chrony.conf"    
    backup: yes
  when: "inventory_hostname == 'controller2'"  
    
- name: Copying over controller3 chrony.conf
  template:
    src: "chrony.conf.c3.j2"
    dest: "/etc/chrony.conf"    
    backup: yes
  when: "inventory_hostname == 'controller3'"  
    
- name: Copying over work chrony.conf
  template:
    src: "chrony.conf.c4.j2"
    dest: "/etc/chrony.conf"    
    backup: yes
  when: "inventory_hostname != 'controller1'
       and inventory_hostname != 'controller2'
       and inventory_hostname != 'controller3'"  
    

- name: Set timezone Asia/Shanghai
  shell: timedatectl set-timezone "Asia/Shanghai"

- name: Restarted chrony service
  service:
    name: chronyd
    state: restarted
    enabled: yes

