---
- name: deploy theguard
  hosts: managers
  vars:
    src_docker_compose: docker-compose.yml
    dest_docker_compose: /etc/thestack/compose/docker-compose-theguard.yml
    stack_name: theguard
    is_remove: "{{ remove }}"
  tasks:

    - name: Print install_status
      debug:
        msg: "{{ is_remove }}"

    - name: install theguard
      include_role:
        name: theguard
      when:  is_remove == 'false'

    - name: uninstall theguard
      shell: docker stack rm theguard
      when: "is_remove == 'true'"
      run_once: true  
