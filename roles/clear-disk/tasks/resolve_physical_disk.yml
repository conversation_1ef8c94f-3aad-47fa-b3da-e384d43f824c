---
- name: Resolve physical disk for LVM or device mapper
  block:
    - name: Get slaves
      command: ls /sys/class/block/{{ real_device | basename }}/slaves
      register: slaves_result
      changed_when: false
      failed_when: false

    - name: Update real device if slaves exist
      set_fact:
        real_device: "/dev/{{ slaves_result.stdout_lines[0] }}"
      when: slaves_result.rc == 0 and slaves_result.stdout != ""
  when: "'/mapper/' in real_device or '/dm-' in real_device"