version: '3.7'
    
services:
  conductor:
    image: tianwen1:5000/thesc:latest
    hostname: conductor
    networks:
      - traefik_prod
    configs:
      - source: conductor.yml
        target: /etc/dayu/conductor.yml
      - source: conductor.py
        target: /thesc/settings.py
    command: ["python3", "-u", "/usr/local/bin/nameko", "run", "--config", "/etc/dayu/conductor.yml", "dayu.conductor.service"]
    deploy:
      replicas: 1
      placement:
        constraints:
          - node.role==manager 
      labels:
        - traefik.enable=true
        - traefik.docker.network=traefik_prod
        - traefik.tcp.routers.conductor.rule=HostSNI(`*`)
        - traefik.tcp.routers.conductor.entrypoints=conductor
        - traefik.tcp.routers.conductor.tls=false
        - traefik.tcp.routers.conductor.service=conductor
        - traefik.tcp.services.conductor.loadbalancer.server.port=9005
      
  scheduler:
    image: tianwen1:5000/thesc:latest
    hostname: scheduler
    networks:
      - traefik_prod
    configs:
      - source: scheduler.yml
        target: /etc/dayu/scheduler.yml
      - source: scheduler.py
        target: /thesc/settings.py
    command: ["python3", "-u", "/usr/local/bin/nameko", "run", "--config", "/etc/dayu/scheduler.yml", "dayu.scheduler.service"]
    deploy:
      replicas: 1
      placement:
        constraints:
          - node.role==manager
      labels:
        - traefik.enable=true
        - traefik.docker.network=traefik_prod
        - traefik.tcp.routers.scheduler.rule=HostSNI(`*`)
        - traefik.tcp.routers.scheduler.entrypoints=scheduler
        - traefik.tcp.routers.scheduler.tls=false
        - traefik.tcp.routers.scheduler.service=scheduler
        - traefik.tcp.services.scheduler.loadbalancer.server.port=9004

  trigger:
    image: tianwen1:5000/thesc:latest
    hostname: trigger
    networks:
      - traefik_prod
    configs:
      - source: trigger.yml
        target: /etc/dayu/trigger.yml
      - source: trigger.py
        target: /thesc/settings.py
    command: ["python3", "-u", "/usr/local/bin/nameko", "run", "--config", "/etc/dayu/trigger.yml", "dayu.trigger.service"]
    deploy:
      replicas: 1
      placement:
        constraints:
          - node.role==manager
      labels:
        - traefik.enable=true
        - traefik.docker.network=traefik_prod
        - traefik.tcp.routers.trigger.rule=HostSNI(`*`)
        - traefik.tcp.routers.trigger.entrypoints=trigger
        - traefik.tcp.routers.trigger.tls=false
        - traefik.tcp.routers.trigger.service=trigger
        - traefik.tcp.services.trigger.loadbalancer.server.port=9001

  executor:
    image: tianwen1:5000/thesc:latest
    hostname: executor
    networks:
      - traefik_prod
    configs:
      - source: executor.yml
        target: /etc/dayu/executor.yml
      - source: executor.py
        target: /thesc/settings.py
    command: ["python3", "-u", "/usr/local/bin/nameko", "run", "--config", "/etc/dayu/executor.yml", "dayu.executor.service"]
    deploy:
      replicas: 1
      placement:
        constraints:
          - node.role==manager
      labels:
        - traefik.enable=true
        - traefik.docker.network=traefik_prod
        - traefik.tcp.routers.executor.rule=HostSNI(`*`)
        - traefik.tcp.routers.executor.entrypoints=executor
        - traefik.tcp.routers.executor.tls=false
        - traefik.tcp.routers.executor.service=executor
        - traefik.tcp.services.executor.loadbalancer.server.port=9003

  recommend:
    image: tianwen1:5000/thesc:latest
    hostname: recommend
    networks:
      - traefik_prod
    configs:
      - source: recommend.yml
        target: /etc/dayu/recommend.yml
      - source: recommend.py
        target: /thesc/settings.py
    command: ["python3", "-u", "/usr/local/bin/nameko", "run", "--config", "/etc/dayu/recommend.yml", "dayu.recommend.service"]
    deploy:
      replicas: 1
      placement:
        constraints:
          - node.role==manager
      labels:
        - traefik.enable=true
        - traefik.docker.network=traefik_prod
        - traefik.tcp.routers.recommend.rule=HostSNI(`*`)
        - traefik.tcp.routers.recommend.entrypoints=recommend
        - traefik.tcp.routers.recommend.tls=false
        - traefik.tcp.routers.recommend.service=recommend
        - traefik.tcp.services.recommend.loadbalancer.server.port=9002

networks:
  traefik_prod:
    external: true

configs:
  conductor.yml:
    file: /etc/thestack/conf/dayu/conductor.yml
  executor.yml:
    file: /etc/thestack/conf/dayu/executor.yml
  recommend.yml:
    file: /etc/thestack/conf/dayu/recommend.yml
  scheduler.yml:
    file: /etc/thestack/conf/dayu/scheduler.yml
  trigger.yml:
    file: /etc/thestack/conf/dayu/trigger.yml
  conductor.py:
    file: /etc/thestack/conf/theweb/settings.py
  scheduler.py:
    file: /etc/thestack/conf/theweb/settings.py
  trigger.py:
    file: /etc/thestack/conf/theweb/settings.py
  executor.py:
    file: /etc/thestack/conf/theweb/settings.py
  recommend.py:
    file: /etc/thestack/conf/theweb/settings.py    