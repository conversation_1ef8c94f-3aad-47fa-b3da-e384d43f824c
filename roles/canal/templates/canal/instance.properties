# MySQL服务器ID
canal.instance.mysql.slaveId=1234

# Position 信息
canal.instance.master.address={{ vip }}:3306
canal.instance.master.journal.name=
canal.instance.master.position=
canal.instance.master.timestamp=

# 解析器配置
canal.instance.parser.strict.mode=false
canal.instance.parser.parallel=false
canal.instance.parser.buffer.size=32768
canal.instance.parser.tsdb.enable=false

# 网络设置
canal.instance.network.receiveBufferSize=16384
canal.instance.network.sendBufferSize=16384

# 过滤器配置
canal.instance.filter.query.dml=true
canal.instance.filter.query.dcl=false
canal.instance.filter.query.ddl=false
canal.instance.filter.regex=hci_db\\.users

# 认证配置
canal.instance.dbUsername=root
canal.instance.dbPassword=thecloud2015.1

# 字符集和编码
canal.instance.connectionCharset=UTF-8

# Binlog配置
canal.instance.gtidon=false
canal.instance.binlog.format=ROW
canal.instance.master.binlog.format=ROW

# 大小写敏感
canal.instance.master.lower_case_table_names=1

# 全局设置
canal.instance.global.mode=spring
canal.instance.global.lazy=false
canal.instance.global.ddl=true

# 重要：添加以下配置
canal.instance.master.standby=false
canal.instance.rds.accesskey=
canal.instance.rds.secretkey=
canal.instance.rds.instanceId=

# 故障恢复
canal.instance.fallbackIntervalInSeconds=60

# 禁用心跳检测
canal.instance.detecting.enable=false

# 禁用自动切换
canal.instance.fallbackIntervalInSeconds=60
canal.instance.heartbeatHaEnable=false