[Unit]
Description=libvirt-exporter

After=network-online.target local-fs.target time-sync.target
Wants=network-online.target local-fs.target time-sync.target


[Service]
LimitNOFILE=1048576
LimitNPROC=1048576
EnvironmentFile=-/etc/environment
ExecStartPre=-/usr/bin/docker rm libvirt-exporter
ExecStart=-/usr/bin/docker run  -p 9177:9177  --network host -v /var/run/libvirt:/var/run/libvirt --name libvirt-exporter -t tianwen1:5000/libvirt-exporter:latest
ExecStop=-/usr/bin/docker stop libvirt-exporter
KillMode=none
Restart=on-failure
RestartSec=10s
TimeoutStartSec=120
TimeoutStopSec=120
StartLimitInterval=30min
StartLimitBurst=5

[Install]
WantedBy=default.target
