---
- name: deploy conf
  hosts: managers
  vars:
    src_conf: conf
    dest_conf: /etc/thestack/
    src_docker_compose: docker-compose.yml
    dest_docker_compose: /etc/thestack/compose/docker-compose-endconf.yml
    src_prometheus_conf: prometheus.yml.j2
    dest_prometheus_conf: /etc/thestack/conf/prometheus/prometheus.yml
    src1_prometheus_conf: prometheus1.yml.j2
    dest1_prometheus_conf: /etc/thestack/conf/prometheus/prometheus.yml
    stack_name: theendconf
  tasks:

    #检测docker版本
    - name: Check if node is in Docker swarm cluster
      shell: 'docker --version'
      register: docker_swarm_node_state

    - name: Print key-value pairs
      debug:
        msg: "{{ docker_swarm_node_state.stdout }}"
      
    #非幂等性安装
    - name: Include role config_reload
      include_role:
        name: config_prometheus
      when: docker_swarm_node_state.stdout not in docker_version_re

    #幂等性安装
    - name: Include role config
      include_role:
        name: config_prometheus_reload
      when: docker_swarm_node_state.stdout in docker_version_re