---
- name: Ensure traefik config directory exists
  file:
    path: "{{ dest_traefik_routter_conf | dirname }}"
    state: directory
    mode: '0755'
    recurse: yes

- name: Ensure log directory exists
  file:
    path: "{{ '/var/log/the' | dirname }}"
    state: directory
    mode: '0755'
    recurse: yes

- name: upload traefik routter conf
  template:
    src: "{{ src_traefik_routter_conf }}"
    dest: "{{ dest_traefik_routter_conf }}"
    force: yes
    mode: '0644'    # 可选：设置文件权限
    backup: yes     # 可选：在覆盖前创建备份

- name: upload traefik cert conf
  template:
    src: "{{ src_traefik_cert }}"
    dest: "{{ dest_traefik_cert }}"
    force: yes
    mode: '0644'    # 可选：设置文件权限
    backup: yes     # 可选：在覆盖前创建备份

- name: upload traefik key conf
  template:
    src: "{{ src_traefik_key }}"
    dest: "{{ dest_traefik_key }}"
    force: yes
    mode: '0644'    # 可选：设置文件权限
    backup: yes     # 可选：在覆盖前创建备份

- name: upload tcp route conf
  template:
    src: "{{ src_traefik_tcp }}"
    dest: "{{ dest_traefik_tcp }}"
    force: yes
    mode: '0644'    # 可选：设置文件权限
    backup: yes     # 可选：在覆盖前创建备份

- name: Upload docker compose file
  template:
    src: "{{ src_docker_compose }}"
    dest: "{{ dest_docker_compose }}"
    force: yes
    mode: '0644'    # 可选：设置文件权限
    backup: yes     # 可选：在覆盖前创建备份


# - name: Ensure rabbitmq_net network exists
#   ansible.builtin.shell: |
#     if ! docker network ls | grep -q rabbitmq_net; then
#       docker network create -d overlay rabbitmq_net
#     fi



- name: Deploy services
  shell: docker stack deploy -c {{ dest_docker_compose }} {{ stack_name }}
  run_once: true
  
- name: Pause for 10 seconds to wait for services being running
  pause:
    seconds: 10
  run_once: true

- name: Ensure services deployed
  shell: docker service ls
  register: service_output

- name: Output service list
  debug:
    msg: "{{ service_output.stdout_lines }}"

- name: Show stacks
  shell: docker stack list