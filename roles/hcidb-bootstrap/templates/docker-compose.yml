version: "3.7"

services:
  hcidb-bootstrap:
    image: tianwen1:5000/hci_db-bootstrap:latest
    hostname: bootstrap
    network_mode: "service:traefik_reverse-proxy"
    configs:
      - source: hcidb-bootstrap.py
        target: /code/settings.py
      - source: hcidb-bootstrap.py
        target: /hci_api/settings.py
    deploy:
      replicas: 1
      placement:
        constraints:
          - node.role==manager
      restart_policy:
        condition: on-failure
        delay: 3s
        max_attempts: 5
        window: 60s

configs:
  hcidb-bootstrap.py:
    file: /etc/thestack/conf/hcidb/settings.py