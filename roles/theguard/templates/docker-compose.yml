version: "3.7"
services:
  theguard:
    hostname: theguard
    image: tianwen1:5000/the-guard:latest
    configs:
      - source: theguard.ini
        target: /opt/unhealthy-guard/conf/app.ini
    networks:
      - hostnet
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - /usr/share/zoneinfo/Asia/Shanghai:/etc/localtime
      - /etc/hostname:/etc/host_name
    deploy:
      mode: global
      placement:
        constraints:
          - node.role == manager


networks:
  hostnet:
    external: true
    name: host

configs:
  theguard.ini:
    file: /etc/thestack/conf/theguard/config.ini