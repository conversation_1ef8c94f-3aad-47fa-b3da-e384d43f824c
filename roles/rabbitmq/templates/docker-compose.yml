version: "3.6"
networks:
  traefik_prod:
    external: true
    
services:
  rabbitmq:
    image: tianwen1:5000/mao/rabbitmq:3.7.8-management
    hostname: rabbitmq
    environment:
      - CLUSTERED=false
      - RABBITMQ_DEFAULT_USER=openstack
      - RABBITMQ_DEFAULT_PASS=openstack
      - RABBITMQ_ERLANG_COOKIE="MY-SECRET-KEY-123"
    networks:
      - traefik_prod
    deploy:
      replicas: 1
      restart_policy:
        condition: any
        delay: 10s
        max_attempts: 3
        window: 180s
      placement:
        constraints:
          - node.role==manager
      labels:
        - traefik.enable=true
        - traefik.docker.network=traefik_prod
        
        - traefik.tcp.routers.rabbitmq.rule=HostSNI(`*`)
        - traefik.tcp.routers.rabbitmq.entrypoints=rabbitmq
        - traefik.tcp.routers.rabbitmq.tls=false
        - traefik.tcp.routers.rabbitmq.service=rabbitmq
        - traefik.tcp.services.rabbitmq.loadbalancer.server.port=15672
        
        - traefik.tcp.routers.rabbitmq-connect.rule=HostSNI(`*`)
        - traefik.tcp.routers.rabbitmq-connect.entrypoints=rabbitmq-connect
        - traefik.tcp.routers.rabbitmq-connect.tls=false
        - traefik.tcp.routers.rabbitmq-connect.service=rabbitmq-connect
        - traefik.tcp.services.rabbitmq-connect.loadbalancer.server.port=5672      
