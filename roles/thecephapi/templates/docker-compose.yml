version: "3.7"
services:
  thecephapi:
    hostname: thecephapi
    image: tianwen1:5000/thecephapi:latest
    configs:
      - source: thecephapi.yml
        target: /opt/thecephapi/config.yaml
    networks:
      - traefik_prod
    deploy:
      mode: replicated
      replicas: 1
      restart_policy:
        condition: any
        delay: 10s
        window: 120s
      placement:
        constraints:
          - node.role==manager
      labels:
        - traefik.enable=true
        - traefik.http.middlewares.thecephapi_strip.stripprefix.prefixes=/thecephapi
        - traefik.http.routers.thecephapi.rule=PathPrefix(`/thecephapi`)
        - traefik.http.routers.thecephapi.entrypoints=web
        - traefik.http.services.thecephapi.loadbalancer.server.port=9179
        - traefik.http.routers.thecephapi.middlewares=thecephapi_strip
        - traefik.docker.network=traefik_prod


  thecss:
    image: tianwen1:5000/thecss:latest
    networks:
      - traefik_prod
    volumes:
      - /etc/ceph:/etc/ceph
    configs:
      - source: thecss.properties
        target: /etc/thecss/application.properties
    deploy:
      mode: replicated
      replicas: 1
      restart_policy:
        condition: any
        delay: 10s
        window: 120s
      placement:
        constraints:
          - node.role==manager
      labels:
        - traefik.enable=true
        - traefik.http.middlewares.thecss_strip.stripprefix.prefixes=/thecss
        - traefik.http.routers.thecss.rule=PathPrefix(`/thecss`)
        - traefik.http.routers.thecss.entrypoints=web
        - traefik.http.services.thecss.loadbalancer.server.port=10002
        - traefik.http.routers.thecss.middlewares=thecss_strip
        - traefik.docker.network=traefik_prod

networks:
  traefik_prod:
    external: true

configs:
  thecephapi.yml:
    file: /etc/thestack/conf/thecephapi/thecephapi.yml
  thecss.properties:
    file: /etc/thestack/conf/thecephapi/application.properties